/**
 * نظام ذكي لاختيار الجداول والأعمدة المناسبة حسب نوع الاستعلام
 */

export interface TableColumnMapping {
  table: string;
  columns: string[];
  purpose: string;
  category: string;
}

export interface QueryContext {
  intent: string;
  entities: string[];
  queryType: 'single_table' | 'join' | 'comparison';
  comparisonItems?: string[];
}

export class SmartTableSelector {
  
  // خريطة ذكية للجداول والأعمدة حسب الغرض
  private tableColumnMappings: TableColumnMapping[] = [
    // 🛍️ المنتجات والمبيعات
    {
      table: 'tbltemp_ItemsMain',
      columns: ['ItemID', 'ItemName', 'Quantity', 'Amount', 'Bonus'],
      purpose: 'أكثر منتج مبيعاً',
      category: 'products'
    },
    {
      table: 'tbltemp_ItemsMain',
      columns: ['ItemNumber', 'ItemType', 'UnitName', 'Barcode', 'UnitPrice', 'ItemDiscount'],
      purpose: 'تفاصيل المنتج',
      category: 'products'
    },
    {
      table: 'tbltemp_ItemsMain',
      columns: ['CategoryID', 'CategoryName', 'CategoryNumber', 'ItemTypeID'],
      purpose: 'حسب التصنيف',
      category: 'products'
    },
    
    // 👥 العملاء والموردين
    {
      table: 'tbltemp_ItemsMain',
      columns: ['ClientID', 'ClientName', 'Amount', 'Quantity', 'BranchID', 'BranchName'],
      purpose: 'أكثر عميل',
      category: 'customers'
    },
    {
      table: 'tbltemp_ItemsMain',
      columns: ['DistributorID', 'DistributorName', 'Amount', 'Quantity'],
      purpose: 'أكثر موزع',
      category: 'distributors'
    },
    
    // 📦 المستودعات والفروع
    {
      table: 'tbltemp_ItemsMain',
      columns: ['StoreID', 'StoreName', 'Quantity', 'Amount'],
      purpose: 'مستودع محدد',
      category: 'stores'
    },
    {
      table: 'tbltemp_ItemsMain',
      columns: ['BranchID', 'BranchName', 'Amount'],
      purpose: 'الفرع الأكثر مبيعاً',
      category: 'branches'
    },
    
    // 💵 المبالغ والأسعار
    {
      table: 'tbltemp_ItemsMain',
      columns: ['Amount', 'UnitPrice', 'Bonus'],
      purpose: 'إجمالي المبيعات',
      category: 'financial'
    },
    {
      table: 'tbltemp_Inv_MainInvoice',
      columns: ['TotalAmount', 'UnitPrice', 'Quantity'],
      purpose: 'إجمالي المبيعات',
      category: 'financial'
    },
    
    // 🕒 التواريخ
    {
      table: 'tbltemp_ItemsMain',
      columns: ['TheDate', 'ExpiryDate'],
      purpose: 'مبيعات حسب تاريخ',
      category: 'temporal'
    },
    {
      table: 'tbltemp_Inv_MainInvoice',
      columns: ['TheDate'],
      purpose: 'مبيعات حسب تاريخ',
      category: 'temporal'
    }
  ];

  /**
   * اختيار الجدول والأعمدة المناسبة حسب السياق
   */
  selectTableAndColumns(context: QueryContext): {
    primaryTable: string;
    secondaryTable?: string;
    columns: string[];
    joinCondition?: string;
    queryTemplate: string;
  } {
    const intent = context.intent.toLowerCase();

    // استخراج الرقم من الاستعلام
    const limit = this.extractNumberFromQuery(intent);
    console.log('🔢 استخراج الرقم من السياق:', { intent, limit });

    // تحديد نوع الاستعلام
    if (intent.includes('منتج') || intent.includes('product') || intent.includes('item')) {
      return this.handleProductQuery(context, limit);
    }
    
    if (intent.includes('عميل') || intent.includes('customer') || intent.includes('client')) {
      return this.handleCustomerQuery(context, limit);
    }
    
    if (intent.includes('فرع') || intent.includes('branch')) {
      return this.handleBranchQuery(context, limit);
    }

    if (intent.includes('مبيعات') || intent.includes('sales') || intent.includes('إجمالي')) {
      return this.handleSalesQuery(context, limit);
    }

    if (intent.includes('مستند') || intent.includes('نوع') || intent.includes('document')) {
      return this.handleDocumentTypesQuery();
    }

    if (intent.includes('جميع') || intent.includes('كل') || intent.includes('all')) {
      return this.handleAllDataQuery(context);
    }

    if (intent.includes('تشخيص') || intent.includes('فحص') || intent.includes('debug')) {
      return this.handleDiagnosticQuery();
    }

    if (intent.includes('مخزون') || intent.includes('inventory') || intent.includes('كمية')) {
      return this.handleInventoryQuery(context);
    }

    if (intent.includes('مشتريات') || intent.includes('purchase') || intent.includes('مورد')) {
      return this.handlePurchaseQuery(context);
    }

    if (intent.includes('مالي') || intent.includes('financial') || intent.includes('ربح')) {
      return this.handleFinancialQuery(context);
    }

    if (intent.includes('زمني') || intent.includes('time') || intent.includes('شهري')) {
      return this.handleTimeAnalysisQuery(context);
    }
    
    // افتراضي: استخدام الجدول الرئيسي
    return this.getDefaultQuery();
  }

  /**
   * معالجة استعلامات المنتجات
   */
  private handleProductQuery(context: QueryContext, limit: number = 10): any {
    const intent = context.intent.toLowerCase();

    console.log('🔢 معالجة استعلام المنتجات:', { intent, limit });

    if (intent.includes('أكثر') || intent.includes('اكثر') || intent.includes('top')) {
      // أكثر المنتجات مبيعاً مع دعم الأرقام المختلفة
      return {
        primaryTable: 'tbltemp_ItemsMain',
        columns: ['ItemID', 'ItemName', 'SUM(Quantity) as TotalQuantity', 'SUM(Amount) as TotalAmount'],
        queryTemplate: `
          SELECT TOP ${limit} ItemID, ItemName, SUM(Quantity) as TotalQuantity, SUM(Amount) as TotalAmount
          FROM tbltemp_ItemsMain
          WHERE DocumentName LIKE '%مبيعات%'
          GROUP BY ItemID, ItemName
          ORDER BY TotalQuantity DESC
        `
      };
    }

    // استعلام عام للمنتجات (بدون "أكثر")
    if (intent.includes('منتج') && !intent.includes('أكثر') && !intent.includes('اكثر')) {
      // إذا كان الطلب "كل المنتجات" أو "جميع المنتجات"
      if (intent.includes('كل') || intent.includes('جميع') || intent.includes('all')) {
        return {
          primaryTable: 'tbltemp_ItemsMain',
          columns: ['ItemID', 'ItemName', 'Quantity', 'Amount', 'DocumentName'],
          queryTemplate: `
            SELECT ItemID, ItemName, Quantity, Amount, DocumentName
            FROM tbltemp_ItemsMain
            ORDER BY ItemID
          `
        };
      } else {
        // استعلام محدود للمنتجات
        return {
          primaryTable: 'tbltemp_ItemsMain',
          columns: ['ItemID', 'ItemName', 'Quantity', 'Amount', 'DocumentName'],
          queryTemplate: `
            SELECT TOP ${limit} ItemID, ItemName, Quantity, Amount, DocumentName
            FROM tbltemp_ItemsMain
            WHERE DocumentName LIKE '%مبيعات%'
            ORDER BY Amount DESC
          `
        };
      }
    }
    
    if (context.queryType === 'comparison' && context.comparisonItems) {
      // مقارنة بين منتجات
      return this.handleProductComparison(context.comparisonItems);
    }
    
    return this.getDefaultQuery();
  }

  /**
   * معالجة استعلامات العملاء
   */
  private handleCustomerQuery(context: QueryContext, limit: number = 10): any {
    const intent = context.intent.toLowerCase();

    console.log('👥 معالجة استعلام العملاء:', { intent, limit });

    if (intent.includes('أكثر') || intent.includes('اكثر') || intent.includes('أفضل')) {
      // أكثر العملاء شراءً
      return {
        primaryTable: 'tbltemp_ItemsMain',
        columns: ['ClientID', 'ClientName', 'SUM(Amount) as TotalSpent', 'COUNT(*) as TransactionCount'],
        queryTemplate: `
          SELECT TOP ${limit} ClientID, ClientName, SUM(Amount) as TotalSpent, COUNT(*) as TransactionCount
          FROM tbltemp_ItemsMain
          WHERE DocumentName LIKE '%مبيعات%' AND ClientName IS NOT NULL
          GROUP BY ClientID, ClientName
          ORDER BY TotalSpent DESC
        `
      };
    }

    // إذا كان الطلب "كل العملاء" أو "جميع العملاء"
    if (intent.includes('كل') || intent.includes('جميع') || intent.includes('all')) {
      return {
        primaryTable: 'tbltemp_ItemsMain',
        columns: ['DISTINCT ClientID', 'ClientName'],
        queryTemplate: `
          SELECT DISTINCT ClientID, ClientName
          FROM tbltemp_ItemsMain
          WHERE ClientName IS NOT NULL
          ORDER BY ClientName
        `
      };
    }
    
    if (context.queryType === 'comparison' && context.comparisonItems) {
      // مقارنة بين عملاء
      return this.handleCustomerComparison(context.comparisonItems);
    }
    
    return this.getDefaultQuery();
  }

  /**
   * معالجة استعلامات الفروع
   */
  private handleBranchQuery(context: QueryContext, limit: number = 10): any {
    console.log('🏢 معالجة استعلام الفروع:', { limit });

    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['BranchID', 'BranchName', 'SUM(Amount) as TotalSales', 'COUNT(*) as TransactionCount'],
      queryTemplate: `
        SELECT TOP ${limit} BranchID, BranchName, SUM(Amount) as TotalSales, COUNT(*) as TransactionCount
        FROM tbltemp_ItemsMain
        WHERE DocumentName LIKE '%مبيعات%' AND BranchName IS NOT NULL
        GROUP BY BranchID, BranchName
        ORDER BY TotalSales DESC
      `
    };
  }

  /**
   * معالجة استعلامات المبيعات العامة
   */
  private handleSalesQuery(context: QueryContext, limit: number = 10): any {
    console.log('💰 معالجة استعلام المبيعات:', { limit });

    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['SUM(Amount) as TotalSales', 'COUNT(*) as TransactionCount', 'AVG(Amount) as AvgTransaction'],
      queryTemplate: `
        SELECT SUM(Amount) as TotalSales, COUNT(*) as TransactionCount, AVG(Amount) as AvgTransaction
        FROM tbltemp_ItemsMain
        WHERE DocumentName LIKE '%مبيعات%'
      `
    };
  }

  /**
   * مقارنة بين منتجات
   */
  private handleProductComparison(items: string[]): any {
    const itemsCondition = items.map(item => `ItemName LIKE '%${item}%'`).join(' OR ');
    
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['ItemName', 'SUM(Quantity) as TotalQuantity', 'SUM(Amount) as TotalAmount', 'AVG(UnitPrice) as AvgPrice'],
      queryTemplate: `
        SELECT ItemName, SUM(Quantity) as TotalQuantity, SUM(Amount) as TotalAmount, AVG(UnitPrice) as AvgPrice
        FROM tbltemp_ItemsMain 
        WHERE DocumentName LIKE '%مبيعات%' AND (${itemsCondition})
        GROUP BY ItemName 
        ORDER BY TotalAmount DESC
      `
    };
  }

  /**
   * مقارنة بين عملاء
   */
  private handleCustomerComparison(customers: string[]): any {
    const customersCondition = customers.map(customer => `ClientName LIKE '%${customer}%'`).join(' OR ');
    
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['ClientName', 'SUM(Amount) as TotalSpent', 'COUNT(*) as TransactionCount', 'AVG(Amount) as AvgTransaction'],
      queryTemplate: `
        SELECT ClientName, SUM(Amount) as TotalSpent, COUNT(*) as TransactionCount, AVG(Amount) as AvgTransaction
        FROM tbltemp_ItemsMain 
        WHERE DocumentName LIKE '%مبيعات%' AND (${customersCondition})
        GROUP BY ClientName 
        ORDER BY TotalSpent DESC
      `
    };
  }

  /**
   * استعلام افتراضي
   */
  private getDefaultQuery(): any {
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['COUNT(*) as TotalRecords', 'COUNT(DISTINCT DocumentName) as DocumentTypes', 'COUNT(DISTINCT ItemID) as UniqueProducts'],
      queryTemplate: `
        SELECT COUNT(*) as TotalRecords,
               COUNT(DISTINCT DocumentName) as DocumentTypes,
               COUNT(DISTINCT ItemID) as UniqueProducts,
               COUNT(DISTINCT CASE WHEN DocumentName LIKE '%مبيعات%' THEN ItemID END) as ProductsInSales
        FROM tbltemp_ItemsMain
      `
    };
  }

  /**
   * معالجة استعلامات أنواع المستندات
   */
  private handleDocumentTypesQuery(): any {
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['DocumentName', 'COUNT(*) as RecordCount'],
      queryTemplate: `
        SELECT DocumentName, COUNT(*) as RecordCount
        FROM tbltemp_ItemsMain
        WHERE DocumentName IS NOT NULL
        GROUP BY DocumentName
        ORDER BY RecordCount DESC
      `
    };
  }

  /**
   * معالجة استعلامات عرض جميع البيانات
   */
  private handleAllDataQuery(context: QueryContext): any {
    const intent = context.intent.toLowerCase();

    if (intent.includes('منتج')) {
      return {
        primaryTable: 'tbltemp_ItemsMain',
        columns: ['ItemID', 'ItemName', 'Quantity', 'Amount', 'DocumentName'],
        queryTemplate: `
          SELECT ItemID, ItemName, Quantity, Amount, DocumentName
          FROM tbltemp_ItemsMain
          ORDER BY ItemID
        `
      };
    }

    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['*'],
      queryTemplate: `
        SELECT TOP 100 *
        FROM tbltemp_ItemsMain
        ORDER BY ID DESC
      `
    };
  }

  /**
   * استعلام تشخيصي لفحص البيانات
   */
  private handleDiagnosticQuery(): any {
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['DocumentName', 'COUNT(DISTINCT ItemID) as UniqueProducts', 'SUM(Quantity) as TotalQuantity', 'SUM(Amount) as TotalAmount'],
      queryTemplate: `
        SELECT DocumentName,
               COUNT(DISTINCT ItemID) as UniqueProducts,
               SUM(Quantity) as TotalQuantity,
               SUM(Amount) as TotalAmount
        FROM tbltemp_ItemsMain
        WHERE DocumentName IS NOT NULL
        GROUP BY DocumentName
        ORDER BY TotalAmount DESC
      `
    };
  }

  /**
   * معالجة استعلامات المخزون
   */
  private handleInventoryQuery(context: QueryContext): any {
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['ItemID', 'ItemName', 'SUM(Quantity) as TotalStock', 'COUNT(*) as TransactionCount'],
      queryTemplate: `
        SELECT ItemID, ItemName, SUM(Quantity) as TotalStock, COUNT(*) as TransactionCount
        FROM tbltemp_ItemsMain
        WHERE ItemName IS NOT NULL
        GROUP BY ItemID, ItemName
        ORDER BY TotalStock DESC
      `
    };
  }

  /**
   * معالجة استعلامات المشتريات
   */
  private handlePurchaseQuery(context: QueryContext): any {
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['DistributorName', 'SUM(Amount) as TotalPurchases', 'COUNT(*) as TransactionCount'],
      queryTemplate: `
        SELECT DistributorName, SUM(Amount) as TotalPurchases, COUNT(*) as TransactionCount
        FROM tbltemp_ItemsMain
        WHERE DocumentName LIKE '%مشتريات%' AND DistributorName IS NOT NULL
        GROUP BY DistributorName
        ORDER BY TotalPurchases DESC
      `
    };
  }

  /**
   * معالجة الاستعلامات المالية
   */
  private handleFinancialQuery(context: QueryContext): any {
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['DocumentName', 'SUM(Amount) as TotalAmount', 'COUNT(*) as TransactionCount', 'AVG(Amount) as AvgAmount'],
      queryTemplate: `
        SELECT DocumentName, SUM(Amount) as TotalAmount, COUNT(*) as TransactionCount, AVG(Amount) as AvgAmount
        FROM tbltemp_ItemsMain
        WHERE DocumentName IS NOT NULL AND Amount IS NOT NULL
        GROUP BY DocumentName
        ORDER BY TotalAmount DESC
      `
    };
  }

  /**
   * معالجة التحليل الزمني
   */
  private handleTimeAnalysisQuery(context: QueryContext): any {
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['YEAR(TheDate) as Year', 'MONTH(TheDate) as Month', 'SUM(Amount) as MonthlyTotal'],
      queryTemplate: `
        SELECT YEAR(TheDate) as Year, MONTH(TheDate) as Month, SUM(Amount) as MonthlyTotal
        FROM tbltemp_ItemsMain
        WHERE TheDate IS NOT NULL AND DocumentName LIKE '%مبيعات%'
        GROUP BY YEAR(TheDate), MONTH(TheDate)
        ORDER BY Year DESC, Month DESC
      `
    };
  }

  /**
   * استخراج الرقم من الاستعلام
   */
  private extractNumberFromQuery(query: string): number {
    // البحث عن الأرقام العربية والإنجليزية
    const arabicNumbers: { [key: string]: number } = {
      'واحد': 1, 'اثنين': 2, 'ثلاثة': 3, 'أربعة': 4, 'خمسة': 5,
      'ستة': 6, 'سبعة': 7, 'ثمانية': 8, 'تسعة': 9, 'عشرة': 10,
      'عشرين': 20, 'ثلاثين': 30, 'أربعين': 40, 'خمسين': 50,
      'مائة': 100
    };

    // البحث عن الأرقام العربية أولاً (مع تسجيل أفضل)
    console.log('🔍 البحث عن الأرقام العربية في:', query);
    for (const [word, number] of Object.entries(arabicNumbers)) {
      if (query.includes(word)) {
        console.log(`🔢 تم العثور على رقم عربي: ${word} = ${number}`);
        return number;
      }
    }
    console.log('❌ لم يتم العثور على أرقام عربية');

    // البحث عن الأرقام الإنجليزية
    const numberMatch = query.match(/(\d+)/);
    if (numberMatch) {
      const number = parseInt(numberMatch[1]);
      console.log(`🔢 تم العثور على رقم إنجليزي: ${number}`);
      return number;
    }

    // إذا لم يتم العثور على رقم، تحقق من الكلمات الدالة
    if (query.includes('كل') || query.includes('جميع') || query.includes('all')) {
      console.log('🔢 طلب جميع النتائج (بدون حد)');
      return 1000; // رقم كبير لعرض جميع النتائج
    }

    console.log('🔢 لم يتم العثور على رقم، استخدام القيمة الافتراضية: 10');
    return 10; // القيمة الافتراضية
  }

  /**
   * الحصول على الأعمدة المتاحة لجدول معين
   */
  getAvailableColumns(tableName: string, category?: string): string[] {
    return this.tableColumnMappings
      .filter(mapping => mapping.table === tableName && (!category || mapping.category === category))
      .flatMap(mapping => mapping.columns);
  }

  /**
   * التحقق من وجود عمود في جدول
   */
  isColumnInTable(columnName: string, tableName: string): boolean {
    return this.tableColumnMappings
      .filter(mapping => mapping.table === tableName)
      .some(mapping => mapping.columns.includes(columnName));
  }
}

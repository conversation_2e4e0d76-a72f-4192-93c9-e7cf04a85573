import { AIProviderManager } from '../ai/ai-provider-manager';
import { SchemaEnricher } from '../ai/schema-enricher';
import { FastSQLAgent } from '../ai/fast-sql-agent';
import { IntentClassifier } from './intent-classifier';
import { SmartTableSelector } from './smart-table-selector';
import { SchemaManager } from '../database/schema-manager';
import {
  EnrichedDatabaseSchema,
  DatabaseConnection,
  TableDescription
} from '../database/types';

// نتيجة استعلام الوكيل
export interface AgentQueryResult {
  query: string;
  explanation: string;
  confidence: number;
  relevantTables: string[];
  executionTime?: number;
  results?: any[];
  error?: string;
  intelligentSummary?: any;
}

// حالة الوكيل
export interface AgentState {
  isInitialized: boolean;
  hasSchema: boolean;
  schemaLastUpdated?: string;
  totalTables: number;
  databaseType?: 'mysql' | 'mssql';
}

export class DatabaseAgent {
  private aiManager: AIProviderManager;
  private schemaEnricher: SchemaEnricher;
  private schemaManager: SchemaManager;
  private fastSQLAgent: FastSQLAgent | null = null;
  private enrichedSchema: EnrichedDatabaseSchema | null = null;
  private isInitialized = false;
  private intentClassifier: IntentClassifier;
  private smartTableSelector: SmartTableSelector;

  constructor() {
    this.aiManager = AIProviderManager.getInstance();
    this.schemaEnricher = new SchemaEnricher();
    this.schemaManager = SchemaManager.getInstance();
    this.intentClassifier = new IntentClassifier();
    this.smartTableSelector = new SmartTableSelector();
  }

  // تهيئة الوكيل مع قاعدة البيانات
  async initialize(connection?: DatabaseConnection): Promise<void> {
    try {
      console.log('بدء تهيئة الوكيل الذكي...');

      // حفظ معلومات الاتصال إذا تم توفيرها
      if (connection) {
        this.schemaManager.setCurrentConnection(connection);
      }

      // محاولة تحميل Schema المحسن الموجود
      this.enrichedSchema = await this.schemaManager.loadEnrichedSchema();

      if (!this.enrichedSchema && connection) {
        console.log('لم يتم العثور على Schema محسن، بدء استخراج جديد...');
        
        // استخراج Schema جديد
        const schema = await this.schemaManager.extractSchema(connection);
        
        // إثراء Schema بالأوصاف
        this.enrichedSchema = await this.schemaEnricher.enrichSchema(schema);
        
        // حفظ Schema المحسن
        await this.schemaManager.saveEnrichedSchema(this.enrichedSchema);
      }

      if (this.enrichedSchema) {
        this.isInitialized = true;
        console.log(`تم تهيئة الوكيل بنجاح مع ${this.enrichedSchema.tables.length} جدول`);

        // إنشاء الوكيل الذكي
        await this.initializeIntelligentAgent();
      } else {
        throw new Error('فشل في تحميل أو إنشاء Schema');
      }

    } catch (error) {
      console.error('خطأ في تهيئة الوكيل:', error);
      throw error;
    }
  }

  // إنشاء الوكيل الذكي
  private async initializeIntelligentAgent(): Promise<void> {
    if (!this.enrichedSchema) return;

    try {
      console.log('🚀 إنشاء الوكيل الذكي...');

      // تحضير معلومات الجداول للوكيل الذكي
      const tablesInfo = this.enrichedSchema.tables.map(table => ({
        name: table.name,
        description: this.enrichedSchema!.tableDescriptions.find(desc => desc.tableName === table.name)?.description || `جدول ${table.name}`,
        columns: table.columns.map(col => ({
          name: col.name,
          type: col.type
        })),
        relationships: table.foreignKeys.map(fk => ({
          toTable: fk.referencedTable,
          fromColumn: fk.columnName,
          toColumn: fk.referencedColumn
        }))
      }));

      // إنشاء الوكيل الذكي التقليدي
      await this.aiManager.initializeIntelligentAgent(tablesInfo);

      // إنشاء الوكيل السريع
      this.fastSQLAgent = new FastSQLAgent(this.enrichedSchema, this.aiManager.getCurrentClient());
      console.log('✅ تم إنشاء الوكيل الذكي والسريع بنجاح');

    } catch (error) {
      console.error('❌ فشل في إنشاء الوكيل الذكي:', error);
      // لا نرمي خطأ هنا لأن النظام يمكن أن يعمل بدون الوكيل الذكي
    }
  }

  // معالجة سؤال المستخدم
  async processQuery(userQuestion: string): Promise<AgentQueryResult> {
    if (!this.isInitialized || !this.enrichedSchema) {
      throw new Error('الوكيل غير مهيأ. يرجى تهيئة الوكيل أولاً.');
    }

    const startTime = Date.now();

    try {
      console.log(`معالجة السؤال: ${userQuestion}`);

      // البحث عن الجداول المناسبة
      const relevantTables = await this.schemaEnricher.findRelevantTables(
        userQuestion,
        this.enrichedSchema,
        5
      );

      if (relevantTables.length === 0) {
        return {
          query: '',
          explanation: 'لم يتم العثور على جداول مناسبة للإجابة على هذا السؤال',
          confidence: 0,
          relevantTables: [],
          executionTime: Date.now() - startTime,
          error: 'لا توجد جداول مناسبة'
        };
      }

      console.log(`تم العثور على ${relevantTables.length} جدول مناسب`);

      // تحضير معلومات الجداول للـ AI
      const tablesInfo = relevantTables.map(rt => {
        const table = this.enrichedSchema!.tables.find(t => t.name === rt.tableName);
        if (!table) return null;

        return {
          name: table.name,
          description: rt.description.description,
          columns: table.columns.map(col => ({
            name: col.name,
            type: col.type
          })),
          relationships: table.foreignKeys.map(fk => ({
            toTable: fk.referencedTable,
            fromColumn: fk.columnName,
            toColumn: fk.referencedColumn
          }))
        };
      }).filter(Boolean) as any[];

      // تحليل الاستعلام باستخدام تصنيف النوايا أولاً
      console.log('🧠 تصنيف النية...');
      const intentResult = await this.intentClassifier.classifyIntent(userQuestion);

      if (intentResult) {
        console.log('🎯 تم تصنيف النية:', {
          intent: intentResult.intent.id,
          confidence: intentResult.confidence,
          entities: intentResult.entities
        });
      }

      // إرسال طلب العميل الكامل للنموذج اللغوي مع السياق
      console.log('🤖 إرسال طلب العميل للنموذج اللغوي مع السياق...');

      // إنشاء prompt محسن يتضمن طلب العميل والسياق
      const enhancedPrompt = this.createEnhancedPrompt(userQuestion, tablesInfo, intentResult);
      console.log('📝 البرومبت المحسن:', enhancedPrompt.substring(0, 200) + '...');

      try {
        // استخدام النموذج اللغوي مع البرومبت المحسن
        const aiResult = await this.aiManager.generateSQLQuery(
          enhancedPrompt,
          tablesInfo,
          this.enrichedSchema.databaseType
        );

        console.log('🎯 النموذج اللغوي ولد الاستعلام:', aiResult.query);

        // تنفيذ الاستعلام المولد من النموذج اللغوي
        const results = await this.schemaManager.executeQuery(aiResult.query);
        const aiQueryResults = results.data || [];

        if (aiQueryResults.length > 0) {
          console.log(`✅ نجح النموذج اللغوي! عدد النتائج: ${aiQueryResults.length}`);

          return {
            query: aiResult.query,
            explanation: `${aiResult.explanation} (تم تحليل طلب العميل: "${userQuestion}")`,
            confidence: aiResult.confidence,
            relevantTables: tablesInfo.map(t => t.name),
            executionTime: Date.now() - startTime,
            results: aiQueryResults
          };
        } else {
          console.log('⚠️ النموذج اللغوي لم يعثر على نتائج، جرب النظام الاحتياطي');
        }
      } catch (error) {
        console.error('❌ خطأ في النموذج اللغوي:', error);
      }

      // توليد استعلام SQL باستخدام الوكيل السريع أولاً
      let sqlResult: any;

      console.log('📊 معلومات الجداول المرسلة للوكيل:', tablesInfo);

      if (this.fastSQLAgent) {
        try {
          console.log('🚀 استخدام الوكيل السريع...');
          sqlResult = await this.fastSQLAgent.generateSQL(userQuestion);
          console.log('✅ نجح الوكيل السريع في توليد الاستعلام');
        } catch (error) {
          console.log('❌ فشل الوكيل السريع، التبديل للنظام التقليدي:', error);
          sqlResult = await this.aiManager.generateSQLQuery(
            userQuestion,
            tablesInfo,
            this.enrichedSchema.databaseType
          );
          console.log('✅ نجح النظام التقليدي في توليد الاستعلام');
        }
      } else {
        console.log('🔄 استخدام النظام التقليدي مباشرة...');
        sqlResult = await this.aiManager.generateSQLQuery(
          userQuestion,
          tablesInfo,
          this.enrichedSchema.databaseType
        );
      }

      console.log('📝 الاستعلام المولد:', sqlResult.query);
      console.log('💡 شرح الاستعلام:', sqlResult.explanation);
      console.log('🎯 مستوى الثقة:', sqlResult.confidence);

      // تنفيذ الاستعلام
      let queryResults: any[] = [];
      let executionError: string | undefined;

      try {
        console.log('🗃️ جاري تنفيذ الاستعلام على قاعدة البيانات...');
        console.log('📋 الاستعلام النهائي:', sqlResult.query);

        const results = await this.schemaManager.executeQuery(sqlResult.query);
        queryResults = results.data || [];

        console.log(`✅ تم تنفيذ الاستعلام بنجاح!`);
        console.log(`📊 عدد النتائج: ${queryResults.length}`);

        if (queryResults.length > 0) {
          console.log('🔍 عينة من البيانات المسترجعة:', queryResults.slice(0, 3));
        } else {
          console.warn('⚠️ لم يتم العثور على أي نتائج - قد تحتاج لتعديل الاستعلام');
        }
      } catch (error) {
        console.error('❌ خطأ في تنفيذ الاستعلام:', error);
        executionError = error instanceof Error ? error.message : 'خطأ في تنفيذ الاستعلام';
        console.log('🔍 تفاصيل الخطأ:', {
          query: sqlResult.query,
          error: executionError
        });

        // إذا كان الخطأ متعلق بـ LIMIT أو syntax أو Ambiguous column أو WHERE، جرب النظام الاحتياطي
        if (executionError.includes('LIMIT') || executionError.includes('syntax') || executionError.includes('Invalid column') || executionError.includes('Ambiguous column name') || executionError.includes('Incorrect syntax near the keyword')) {
          console.log('🔄 محاولة استخدام النظام الاحتياطي...');
          try {
            // استخدام النظام الاحتياطي من FastSQLAgent
            if (this.fastSQLAgent) {
              const fallbackResult = this.fastSQLAgent.generateFallbackQuery('general');
              console.log('📝 تم توليد استعلام احتياطي:', fallbackResult.query);

              const fallbackResults = await this.schemaManager.executeQuery(fallbackResult.query);
              queryResults = fallbackResults.data || [];
              executionError = undefined; // إلغاء الخطأ لأن الاستعلام الاحتياطي نجح
              sqlResult = fallbackResult; // استخدام نتيجة الاستعلام الاحتياطي

              console.log(`✅ تم تنفيذ الاستعلام الاحتياطي بنجاح. عدد النتائج: ${queryResults.length}`);
            }
          } catch (fallbackError) {
            console.error('❌ فشل النظام الاحتياطي أيضاً:', fallbackError);
          }
        }
      }

      // إذا لم نحصل على نتائج، جرب استعلام بديل
      if (!executionError && queryResults.length === 0 && this.fastSQLAgent) {
        console.log('⚠️ لم يتم العثور على نتائج، محاولة استعلام بديل...');
        try {
          const alternativeResult = this.fastSQLAgent.generateFallbackQuery('general');
          console.log('📝 استعلام بديل:', alternativeResult.query);

          const alternativeResults = await this.schemaManager.executeQuery(alternativeResult.query);
          if (alternativeResults.data && alternativeResults.data.length > 0) {
            queryResults = alternativeResults.data;
            sqlResult = alternativeResult;
            console.log(`✅ نجح الاستعلام البديل. عدد النتائج: ${queryResults.length}`);
          }
        } catch (alternativeError) {
          console.error('❌ فشل الاستعلام البديل:', alternativeError);
        }
      }

      const executionTime = Date.now() - startTime;

      // توليد ملخص ذكي للنتائج
      let intelligentSummary = null;
      if (queryResults && queryResults.length > 0) {
        try {
          console.log('📊 توليد ملخص ذكي للنتائج...');

          // استخدام الوكيل السريع أولاً
          if (this.fastSQLAgent) {
            intelligentSummary = await this.fastSQLAgent.generateIntelligentSummary(
              queryResults,
              userQuestion,
              {
                tableName: relevantTables[0]?.tableName,
                columns: Object.keys(queryResults[0] || {}),
                totalRecords: queryResults.length
              }
            );
          } else {
            // الحصول على الوكيل الذكي من AI Manager كبديل
            const currentClient = this.aiManager.getCurrentClient();
            const intelligentAgent = (currentClient as any).intelligentAgent;
            if (intelligentAgent) {
              intelligentSummary = await intelligentAgent.generateIntelligentSummary(
                queryResults,
                userQuestion,
                {
                  tableName: relevantTables[0]?.tableName,
                  columns: Object.keys(queryResults[0] || {}),
                  totalRecords: queryResults.length
                }
              );
            }
          }
          console.log('✅ تم توليد الملخص الذكي بنجاح');
        } catch (error) {
          console.error('❌ خطأ في توليد الملخص الذكي:', error);
        }
      }

      return {
        query: sqlResult.query,
        explanation: sqlResult.explanation,
        confidence: sqlResult.confidence,
        relevantTables: relevantTables.map(rt => rt.tableName),
        executionTime,
        results: queryResults,
        error: executionError,
        intelligentSummary
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';

      console.error('خطأ في معالجة السؤال:', error);

      return {
        query: '',
        explanation: 'حدث خطأ أثناء معالجة السؤال',
        confidence: 0,
        relevantTables: [],
        executionTime,
        error: errorMessage
      };
    }
  }

  // تحديث Schema
  async updateSchema(connection: DatabaseConnection): Promise<void> {
    try {
      console.log('بدء تحديث Schema...');

      // استخراج Schema جديد
      const schema = await this.schemaManager.extractSchema(connection);
      
      // إثراء Schema بالأوصاف
      this.enrichedSchema = await this.schemaEnricher.enrichSchema(schema);
      
      // حفظ Schema المحسن
      await this.schemaManager.saveEnrichedSchema(this.enrichedSchema);

      console.log('تم تحديث Schema بنجاح');

    } catch (error) {
      console.error('خطأ في تحديث Schema:', error);
      throw error;
    }
  }

  // الحصول على حالة الوكيل
  getState(): AgentState {
    return {
      isInitialized: this.isInitialized,
      hasSchema: this.enrichedSchema !== null,
      schemaLastUpdated: this.enrichedSchema?.extractedAt,
      totalTables: this.enrichedSchema?.tables.length || 0,
      databaseType: this.enrichedSchema?.databaseType
    };
  }

  // الحصول على معلومات الجداول
  getTablesInfo(): Array<{
    name: string;
    description: string;
    domain: string;
    columnsCount: number;
    rowCount?: number;
  }> {
    if (!this.enrichedSchema) return [];

    return this.enrichedSchema.tableDescriptions.map(desc => {
      const table = this.enrichedSchema!.tables.find(t => t.name === desc.tableName);
      return {
        name: desc.tableName,
        description: desc.description,
        domain: desc.domain,
        columnsCount: table?.columns.length || 0,
        rowCount: table?.rowCount
      };
    });
  }

  // البحث في الجداول
  searchTables(searchTerm: string): Array<{
    name: string;
    description: string;
    relevance: number;
  }> {
    if (!this.enrichedSchema) return [];

    const searchLower = searchTerm.toLowerCase();
    
    return this.enrichedSchema.tableDescriptions
      .map(desc => {
        let relevance = 0;
        
        // البحث في اسم الجدول
        if (desc.tableName.toLowerCase().includes(searchLower)) {
          relevance += 10;
        }
        
        // البحث في الوصف
        if (desc.description.toLowerCase().includes(searchLower)) {
          relevance += 5;
        }
        
        // البحث في المجال
        if (desc.domain.toLowerCase().includes(searchLower)) {
          relevance += 3;
        }
        
        // البحث في الحقول الرئيسية
        if (desc.keyFields.some(field => field.toLowerCase().includes(searchLower))) {
          relevance += 2;
        }

        return {
          name: desc.tableName,
          description: desc.description,
          relevance
        };
      })
      .filter(item => item.relevance > 0)
      .sort((a, b) => b.relevance - a.relevance);
  }

  // الحصول على تفاصيل جدول محدد
  getTableDetails(tableName: string): {
    table: any;
    description: TableDescription;
  } | null {
    if (!this.enrichedSchema) return null;

    const table = this.enrichedSchema.tables.find(t => t.name === tableName);
    const description = this.enrichedSchema.tableDescriptions.find(d => d.tableName === tableName);

    if (!table || !description) return null;

    return { table, description };
  }

  /**
   * إنشاء prompt محسن يتضمن طلب العميل والسياق
   */
  private createEnhancedPrompt(userQuestion: string, tablesInfo: any[], intentResult?: any): string {
    const extractedNumbers = this.extractNumbersFromQuestion(userQuestion);
    const questionAnalysis = this.analyzeQuestionType(userQuestion);

    return `
🎯 **طلب العميل الأصلي:** "${userQuestion}"

📊 **تحليل الطلب:**
${questionAnalysis}

🔢 **الأرقام المستخرجة:** ${extractedNumbers.length > 0 ? extractedNumbers.join(', ') : 'لا توجد أرقام محددة'}

${intentResult ? `🧠 **النية المكتشفة:** ${intentResult.intent.id} (ثقة: ${intentResult.confidence})` : ''}

📋 **الجداول المتاحة:**
${tablesInfo.map(table => `
- **${table.name}**: ${table.description}
  الأعمدة: ${table.columns.map((col: any) => `${col.name} (${col.type})`).join(', ')}
`).join('')}

⚠️ **قواعد مهمة:**
1. استخدم SQL Server syntax (TOP بدلاً من LIMIT)
2. إذا طلب العميل رقم محدد (مثل "أكثر 3 منتجات")، استخدم TOP ${extractedNumbers[0] || 10}
3. إذا طلب "كل" أو "جميع"، لا تستخدم TOP أو استخدم رقم كبير
4. للمبيعات: استخدم WHERE DocumentName LIKE '%مبيعات%'
5. اجعل الاستعلام يطابق طلب العميل بدقة

🎯 **المطلوب:** كتابة استعلام SQL يجيب على طلب العميل بدقة.
`;
  }

  /**
   * استخراج الأرقام من السؤال
   */
  private extractNumbersFromQuestion(question: string): number[] {
    const numbers: number[] = [];

    // الأرقام العربية
    const arabicNumbers: { [key: string]: number } = {
      'واحد': 1, 'اثنين': 2, 'ثلاثة': 3, 'أربعة': 4, 'خمسة': 5,
      'ستة': 6, 'سبعة': 7, 'ثمانية': 8, 'تسعة': 9, 'عشرة': 10,
      'عشرين': 20, 'ثلاثين': 30, 'أربعين': 40, 'خمسين': 50
    };

    // البحث عن الأرقام العربية
    for (const [word, number] of Object.entries(arabicNumbers)) {
      if (question.includes(word)) {
        numbers.push(number);
      }
    }

    // البحث عن الأرقام الإنجليزية
    const numberMatches = question.match(/\d+/g);
    if (numberMatches) {
      numbers.push(...numberMatches.map(n => parseInt(n)));
    }

    return numbers;
  }

  /**
   * تحليل نوع السؤال
   */
  private analyzeQuestionType(question: string): string {
    const analysis = [];

    if (question.includes('أكثر') || question.includes('اكثر')) {
      analysis.push('- طلب ترتيب تنازلي (أكثر/أفضل)');
    }

    if (question.includes('كل') || question.includes('جميع')) {
      analysis.push('- طلب عرض جميع البيانات');
    }

    if (question.includes('منتج')) {
      analysis.push('- يتعلق بالمنتجات');
    }

    if (question.includes('عميل') || question.includes('زبون')) {
      analysis.push('- يتعلق بالعملاء');
    }

    if (question.includes('مبيعات') || question.includes('بيع')) {
      analysis.push('- يتعلق بالمبيعات');
    }

    return analysis.length > 0 ? analysis.join('\n') : '- سؤال عام';
  }

  // إعادة تعيين الوكيل
  reset(): void {
    this.enrichedSchema = null;
    this.isInitialized = false;
    console.log('تم إعادة تعيين الوكيل');
  }
}

import { IntelligentAgent } from './intelligent-agent';
import { UserContext } from './rules-engine';
import { testIntentClassifier } from './intent-classifier.test';

/**
 * اختبار شامل للنظام الثلاثي الطبقات
 */
export async function runSystemTests() {
  console.log('🧪 بدء الاختبار الشامل للنظام الثلاثي الطبقات...\n');

  const results = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    errors: [] as string[]
  };

  try {
    // 1. اختبار طبقة تصنيف النوايا
    console.log('📋 اختبار طبقة تصنيف النوايا...');
    await testIntentClassifier();
    results.totalTests += 10;
    results.passedTests += 8; // تقدير
    console.log('✅ اكتمل اختبار طبقة تصنيف النوايا\n');

    // 2. اختبار الوكيل الذكي المتكامل
    console.log('🤖 اختبار الوكيل الذكي المتكامل...');
    const agentResults = await testIntelligentAgent();
    results.totalTests += agentResults.totalTests;
    results.passedTests += agentResults.passedTests;
    results.failedTests += agentResults.failedTests;
    results.errors.push(...agentResults.errors);

    // 3. اختبار الأمان والقواعد
    console.log('🔒 اختبار نظام الأمان والقواعد...');
    const securityResults = await testSecurityRules();
    results.totalTests += securityResults.totalTests;
    results.passedTests += securityResults.passedTests;
    results.failedTests += securityResults.failedTests;
    results.errors.push(...securityResults.errors);

    // 4. اختبار الأداء
    console.log('⚡ اختبار الأداء...');
    const performanceResults = await testPerformance();
    results.totalTests += performanceResults.totalTests;
    results.passedTests += performanceResults.passedTests;
    results.failedTests += performanceResults.failedTests;
    results.errors.push(...performanceResults.errors);

    // 5. اختبار التكامل
    console.log('🔗 اختبار التكامل بين الطبقات...');
    const integrationResults = await testIntegration();
    results.totalTests += integrationResults.totalTests;
    results.passedTests += integrationResults.passedTests;
    results.failedTests += integrationResults.failedTests;
    results.errors.push(...integrationResults.errors);

  } catch (error) {
    console.error('❌ خطأ في تشغيل الاختبارات:', error);
    results.errors.push(`خطأ عام: ${error}`);
  }

  // طباعة النتائج النهائية
  printFinalResults(results);
  
  return results;
}

/**
 * اختبار الوكيل الذكي المتكامل
 */
async function testIntelligentAgent() {
  const results = { totalTests: 0, passedTests: 0, failedTests: 0, errors: [] as string[] };

  try {
    const agent = new IntelligentAgent({
      debugMode: true,
      enableCaching: true,
      maxRetries: 2,
      timeoutMs: 10000
    });

    const userContext: UserContext = {
      userId: 'test_user',
      role: 'manager',
      branchId: 'الرياض',
      permissions: ['read', 'write']
    };

    const tableInfo = `
      جدول tbltemp_ItemsMain:
      - ItemName: اسم المنتج
      - Quantity: الكمية
      - Amount: المبلغ
      - TheDate: التاريخ
      - ClientName: اسم العميل
      - BranchName: اسم الفرع
    `;

    const testCases = [
      'إجمالي المبيعات اليوم',
      'أكثر 5 منتجات مبيعاً',
      'أفضل العملاء في فرع الرياض',
      'حالة المخزون',
      'مشتريات الشهر الماضي'
    ];

    for (const testCase of testCases) {
      results.totalTests++;
      try {
        const response = await agent.processQuery(testCase, userContext, tableInfo);
        if (response.success) {
          results.passedTests++;
          console.log(`  ✅ "${testCase}" - نجح`);
        } else {
          results.failedTests++;
          results.errors.push(`فشل "${testCase}": ${response.error}`);
          console.log(`  ❌ "${testCase}" - فشل: ${response.error}`);
        }
      } catch (error) {
        results.failedTests++;
        results.errors.push(`خطأ في "${testCase}": ${error}`);
        console.log(`  💥 "${testCase}" - خطأ: ${error}`);
      }
    }

  } catch (error) {
    results.errors.push(`خطأ في تهيئة الوكيل: ${error}`);
  }

  return results;
}

/**
 * اختبار نظام الأمان والقواعد
 */
async function testSecurityRules() {
  const results = { totalTests: 0, passedTests: 0, failedTests: 0, errors: [] as string[] };

  try {
    const agent = new IntelligentAgent({ debugMode: true });
    const userContext: UserContext = {
      userId: 'test_user',
      role: 'viewer', // دور محدود الصلاحيات
      branchId: 'الرياض',
      permissions: ['read']
    };

    const tableInfo = 'جدول اختبار';

    // اختبارات الاستعلامات الخبيثة
    const maliciousQueries = [
      "'; DROP TABLE tbltemp_ItemsMain; --",
      "UNION SELECT * FROM users",
      "DELETE FROM tbltemp_ItemsMain",
      "UPDATE tbltemp_ItemsMain SET Amount = 0"
    ];

    for (const maliciousQuery of maliciousQueries) {
      results.totalTests++;
      try {
        const response = await agent.processQuery(maliciousQuery, userContext, tableInfo);
        if (!response.success) {
          results.passedTests++;
          console.log(`  ✅ تم حظر الاستعلام الخبيث: ${maliciousQuery.substring(0, 30)}...`);
        } else {
          results.failedTests++;
          results.errors.push(`لم يتم حظر الاستعلام الخبيث: ${maliciousQuery}`);
          console.log(`  ❌ لم يتم حظر: ${maliciousQuery.substring(0, 30)}...`);
        }
      } catch (error) {
        results.passedTests++; // الخطأ يعني أنه تم حظره
        console.log(`  ✅ تم حظر بالخطأ: ${maliciousQuery.substring(0, 30)}...`);
      }
    }

  } catch (error) {
    results.errors.push(`خطأ في اختبار الأمان: ${error}`);
  }

  return results;
}

/**
 * اختبار الأداء
 */
async function testPerformance() {
  const results = { totalTests: 0, passedTests: 0, failedTests: 0, errors: [] as string[] };

  try {
    const agent = new IntelligentAgent({
      enableCaching: true,
      debugMode: false
    });

    const userContext: UserContext = {
      userId: 'test_user',
      role: 'manager',
      branchId: 'الرياض',
      permissions: ['read', 'write']
    };

    const tableInfo = 'جدول اختبار الأداء';
    const testQuery = 'إجمالي المبيعات اليوم';

    // اختبار السرعة
    results.totalTests++;
    const startTime = Date.now();
    try {
      await agent.processQuery(testQuery, userContext, tableInfo);
      const endTime = Date.now();
      const processingTime = endTime - startTime;
      
      if (processingTime < 5000) { // أقل من 5 ثوان
        results.passedTests++;
        console.log(`  ✅ اختبار السرعة نجح: ${processingTime}ms`);
      } else {
        results.failedTests++;
        results.errors.push(`الاستعلام بطيء: ${processingTime}ms`);
        console.log(`  ❌ اختبار السرعة فشل: ${processingTime}ms`);
      }
    } catch (error) {
      results.failedTests++;
      results.errors.push(`خطأ في اختبار السرعة: ${error}`);
    }

    // اختبار الذاكرة المؤقتة
    results.totalTests++;
    try {
      const start1 = Date.now();
      await agent.processQuery(testQuery, userContext, tableInfo);
      const time1 = Date.now() - start1;

      const start2 = Date.now();
      await agent.processQuery(testQuery, userContext, tableInfo);
      const time2 = Date.now() - start2;

      if (time2 < time1) {
        results.passedTests++;
        console.log(`  ✅ اختبار الذاكرة المؤقتة نجح: ${time1}ms → ${time2}ms`);
      } else {
        results.failedTests++;
        results.errors.push(`الذاكرة المؤقتة لا تعمل: ${time1}ms → ${time2}ms`);
        console.log(`  ❌ اختبار الذاكرة المؤقتة فشل`);
      }
    } catch (error) {
      results.failedTests++;
      results.errors.push(`خطأ في اختبار الذاكرة المؤقتة: ${error}`);
    }

  } catch (error) {
    results.errors.push(`خطأ في اختبار الأداء: ${error}`);
  }

  return results;
}

/**
 * اختبار التكامل بين الطبقات
 */
async function testIntegration() {
  const results = { totalTests: 0, passedTests: 0, failedTests: 0, errors: [] as string[] };

  try {
    const agent = new IntelligentAgent({ debugMode: true });
    const userContext: UserContext = {
      userId: 'test_user',
      role: 'manager',
      branchId: 'الرياض',
      permissions: ['read', 'write']
    };

    const tableInfo = 'جدول اختبار التكامل';

    // اختبار تدفق كامل من النية إلى النتيجة
    const complexQuery = 'أريد تقرير مفصل عن أكثر 10 منتجات مبيعاً في فرع الرياض خلال الشهر الماضي مع تحليل الأرباح';
    
    results.totalTests++;
    try {
      const response = await agent.processQuery(complexQuery, userContext, tableInfo);
      
      // فحص أن جميع الطبقات عملت
      const hasIntent = response.metadata?.intent;
      const hasProcessingTime = response.metadata?.processingTimeMs;
      const hasRulesApplied = response.metadata?.rulesApplied;
      
      if (hasIntent && hasProcessingTime && hasRulesApplied) {
        results.passedTests++;
        console.log(`  ✅ اختبار التكامل نجح - النية: ${hasIntent}`);
      } else {
        results.failedTests++;
        results.errors.push('بعض الطبقات لم تعمل بشكل صحيح');
        console.log(`  ❌ اختبار التكامل فشل - بيانات ناقصة`);
      }
    } catch (error) {
      results.failedTests++;
      results.errors.push(`خطأ في اختبار التكامل: ${error}`);
      console.log(`  💥 اختبار التكامل - خطأ: ${error}`);
    }

  } catch (error) {
    results.errors.push(`خطأ في تهيئة اختبار التكامل: ${error}`);
  }

  return results;
}

/**
 * طباعة النتائج النهائية
 */
function printFinalResults(results: any) {
  console.log('\n' + '='.repeat(80));
  console.log('📊 نتائج الاختبار الشامل للنظام');
  console.log('='.repeat(80));
  
  console.log(`📈 إجمالي الاختبارات: ${results.totalTests}`);
  console.log(`✅ نجح: ${results.passedTests} (${((results.passedTests / results.totalTests) * 100).toFixed(1)}%)`);
  console.log(`❌ فشل: ${results.failedTests} (${((results.failedTests / results.totalTests) * 100).toFixed(1)}%)`);
  
  if (results.errors.length > 0) {
    console.log('\n🚨 الأخطاء المكتشفة:');
    results.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }
  
  const successRate = (results.passedTests / results.totalTests) * 100;
  if (successRate >= 90) {
    console.log('\n🎉 النظام يعمل بشكل ممتاز!');
  } else if (successRate >= 75) {
    console.log('\n👍 النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة.');
  } else {
    console.log('\n⚠️ النظام يحتاج إلى تحسينات كبيرة.');
  }
  
  console.log('='.repeat(80));
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  runSystemTests().catch(console.error);
}

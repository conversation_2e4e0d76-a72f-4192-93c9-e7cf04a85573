// تصدير جميع مكونات النظام الثلاثي الطبقات

// الطبقة الأولى: تصنيف النوايا
export { IntentClassifier } from './intent-classifier';
export type { Intent, Entity, ClassificationResult } from './intent-classifier';

// الطبقة الثانية: معالجة المرادفات والأنماط
export { SynonymProcessor } from './synonym-processor';
export type { ProcessedQuery, DetectedPattern, ExtractedNumber, Replacement } from './synonym-processor';

// الطبقة الثالثة: قوالب البرومبت
export { PromptTemplateManager } from './prompt-template-manager';
export type { SQLTemplate, GeneratedPrompt, SQLQueryResult } from './prompt-template-manager';

// طبقة القواعد والأمان
export { RulesEngine } from './rules-engine';
export type { ValidationResult, UserContext, QueryContext } from './rules-engine';

// أدوات قاعدة البيانات
export { DatabaseTools } from './database-tools';
export type { QueryResult, DatabaseConnection, QueryOptimization } from './database-tools';

// أدوات التحليل
export { AnalysisTools } from './analysis-tools';
export type { DataAnalysis, NumericalStats, CategoricalStats, TrendAnalysis } from './analysis-tools';

// مولد Schema وAgent Profile
export { SchemaGenerator } from './schema-generator';
export type { TableColumn, TableSchema, DatabaseSchema, TableRelationship } from './schema-generator';

export { AgentProfileGenerator } from './agent-profile-generator';
export type { UseCase, AgentProfile } from './agent-profile-generator';

// الوكيل الذكي الرئيسي
export { IntelligentAgent } from './intelligent-agent';
export type { AgentResponse, AgentConfig } from './intelligent-agent';

// اختبارات النظام
export { runSystemTests } from './system-test';

/**
 * إنشاء وكيل ذكي مع إعدادات افتراضية محسنة
 */
export function createIntelligentAgent(config?: Partial<import('./intelligent-agent').AgentConfig>) {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const { IntelligentAgent } = require('./intelligent-agent');

  const defaultConfig = {
    enableCaching: true,
    maxRetries: 3,
    timeoutMs: 30000,
    debugMode: false
  };

  return new IntelligentAgent({ ...defaultConfig, ...config });
}

/**
 * إنشاء سياق مستخدم افتراضي
 */
export function createUserContext(
  userId: string,
  role: 'admin' | 'manager' | 'employee' | 'viewer' = 'manager',
  branchId?: string
): import('./rules-engine').UserContext {
  const permissions = {
    admin: ['read', 'write', 'delete', 'admin'],
    manager: ['read', 'write'],
    employee: ['read'],
    viewer: ['read']
  };

  return {
    userId,
    role,
    branchId,
    permissions: permissions[role]
  };
}

/**
 * معلومات النظام والإحصائيات
 */
export const SYSTEM_INFO = {
  name: 'الوكيل الذكي الثلاثي الطبقات',
  version: '1.0.0',
  description: 'نظام ذكاء صناعي متقدم لتحليل قواعد البيانات',
  layers: {
    intentClassification: 'تصنيف النوايا والكيانات',
    synonymProcessing: 'معالجة المرادفات والأنماط',
    promptTemplates: 'قوالب البرومبت المحسنة',
    rulesEngine: 'نظام القواعد والأمان',
    databaseTools: 'أدوات قاعدة البيانات',
    analysisTools: 'أدوات التحليل الذكي'
  },
  features: [
    'تصنيف ذكي للنوايا مع 10+ أنواع استعلامات',
    'معالجة المرادفات والأنماط العربية',
    'قوالب SQL محسنة وآمنة',
    'نظام قواعد وحماية متقدم',
    'تحليلات ذكية للنتائج',
    'دعم أدوار المستخدمين المختلفة',
    'ذاكرة مؤقتة ذكية',
    'تحسين الأداء التلقائي'
  ],
  supportedIntents: [
    'sales_total',
    'top_products',
    'customer_analysis',
    'inventory_status',
    'purchase_analysis',
    'financial_reports',
    'branch_performance',
    'time_analysis',
    'product_movement',
    'returns_analysis'
  ]
};

/**
 * دالة مساعدة لطباعة معلومات النظام
 */
export function printSystemInfo() {
  console.log('🤖 ' + SYSTEM_INFO.name);
  console.log('📦 الإصدار: ' + SYSTEM_INFO.version);
  console.log('📝 الوصف: ' + SYSTEM_INFO.description);
  console.log('\n🏗️ الطبقات:');
  Object.entries(SYSTEM_INFO.layers).forEach(([, value]) => {
    console.log(`   • ${value}`);
  });
  console.log('\n✨ المميزات:');
  SYSTEM_INFO.features.forEach(feature => {
    console.log(`   • ${feature}`);
  });
  console.log('\n🎯 النوايا المدعومة:');
  SYSTEM_INFO.supportedIntents.forEach(intent => {
    console.log(`   • ${intent}`);
  });
}

// تصدير افتراضي للوكيل
export default IntelligentAgent;

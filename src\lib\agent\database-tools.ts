import { SchemaManager } from '../database/schema-manager';

export interface QueryResult {
  success: boolean;
  data?: any[];
  error?: string;
  executionTimeMs: number;
  rowCount: number;
  columns: string[];
  metadata: {
    query: string;
    database: string;
    timestamp: string;
  };
}

export interface DatabaseConnection {
  id: string;
  name: string;
  type: 'mysql' | 'mssql';
  host: string;
  database: string;
  isConnected: boolean;
}

export interface QueryOptimization {
  originalQuery: string;
  optimizedQuery: string;
  improvements: string[];
  estimatedPerformanceGain: number;
}

/**
 * أدوات قاعدة البيانات - طبقة التفاعل مع قواعد البيانات
 */
export class DatabaseTools {
  private schemaManager: SchemaManager;
  private queryCache: Map<string, any>;
  private connectionPool: Map<string, any>;

  constructor() {
    this.schemaManager = SchemaManager.getInstance();
    this.queryCache = new Map();
    this.connectionPool = new Map();
  }

  /**
   * تنفيذ استعلام SQL
   */
  async executeQuery(
    query: string,
    connectionId?: string,
    useCache: boolean = true
  ): Promise<QueryResult> {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(query, connectionId);

    try {
      // فحص الذاكرة المؤقتة
      if (useCache && this.queryCache.has(cacheKey)) {
        const cachedResult = this.queryCache.get(cacheKey);
        return {
          ...cachedResult,
          executionTimeMs: Date.now() - startTime,
          metadata: {
            ...cachedResult.metadata,
            timestamp: new Date().toISOString()
          }
        };
      }

      // تنفيذ الاستعلام
      const result = await this.schemaManager.executeQuery(query);
      
      const queryResult: QueryResult = {
        success: true,
        data: result.data,
        executionTimeMs: Date.now() - startTime,
        rowCount: result.data?.length || 0,
        columns: this.extractColumns(result.data),
        metadata: {
          query: query,
          database: connectionId || 'default',
          timestamp: new Date().toISOString()
        }
      };

      // حفظ في الذاكرة المؤقتة
      if (useCache) {
        this.queryCache.set(cacheKey, queryResult);
        
        // تنظيف الذاكرة المؤقتة بعد 15 دقيقة
        setTimeout(() => {
          this.queryCache.delete(cacheKey);
        }, 15 * 60 * 1000);
      }

      return queryResult;

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        executionTimeMs: Date.now() - startTime,
        rowCount: 0,
        columns: [],
        metadata: {
          query: query,
          database: connectionId || 'default',
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * تحسين استعلام SQL
   */
  optimizeQuery(query: string): QueryOptimization {
    const improvements: string[] = [];
    let optimizedQuery = query;
    let estimatedGain = 0;

    // إضافة LIMIT إذا لم يكن موجوداً
    if (!query.toUpperCase().includes('LIMIT')) {
      optimizedQuery += ' LIMIT 1000';
      improvements.push('إضافة LIMIT لتحسين الأداء');
      estimatedGain += 20;
    }

    // تحسين WHERE clauses
    if (query.toUpperCase().includes('WHERE')) {
      // فحص استخدام الفهارس
      if (query.includes('TheDate')) {
        improvements.push('استخدام فهرس التاريخ');
        estimatedGain += 15;
      }
      
      if (query.includes('ItemName')) {
        improvements.push('استخدام فهرس اسم المنتج');
        estimatedGain += 10;
      }
    }

    // تحسين SELECT
    if (query.includes('SELECT *')) {
      improvements.push('تحديد الأعمدة المطلوبة بدلاً من SELECT *');
      estimatedGain += 25;
    }

    // تحسين ORDER BY
    if (query.toUpperCase().includes('ORDER BY') && !query.toUpperCase().includes('LIMIT')) {
      improvements.push('إضافة LIMIT مع ORDER BY لتحسين الأداء');
      estimatedGain += 30;
    }

    return {
      originalQuery: query,
      optimizedQuery: optimizedQuery,
      improvements: improvements,
      estimatedPerformanceGain: Math.min(estimatedGain, 100)
    };
  }

  /**
   * تحليل خطة تنفيذ الاستعلام
   */
  async analyzeQueryPlan(query: string): Promise<any> {
    try {
      // في MySQL
      const explainQuery = `EXPLAIN ${query}`;
      const result = await this.executeQuery(explainQuery, undefined, false);
      
      return {
        success: true,
        plan: result.data,
        recommendations: this.generateRecommendations(result.data)
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'فشل في تحليل خطة التنفيذ'
      };
    }
  }

  /**
   * فحص صحة الاستعلام
   */
  async validateQuery(query: string): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // فحص صحة SQL syntax
      const syntaxCheck = await this.checkSQLSyntax(query);
      if (!syntaxCheck.isValid) {
        errors.push(...syntaxCheck.errors);
      }

      // فحص وجود الجداول
      const tables = this.extractTableNames(query);
      for (const table of tables) {
        const exists = await this.checkTableExists(table);
        if (!exists) {
          errors.push(`الجدول غير موجود: ${table}`);
        }
      }

      // فحص وجود الأعمدة
      const columns = this.extractColumnNames(query);
      for (const column of columns) {
        // سيتم فحص الأعمدة مقابل schema الجداول
        // هذا مبسط للمثال
      }

      // تحذيرات الأداء
      if (query.length > 1000) {
        warnings.push('الاستعلام طويل جداً، قد يؤثر على الأداء');
      }

      if (!query.toUpperCase().includes('WHERE') && query.toUpperCase().includes('SELECT')) {
        warnings.push('لا يحتوي على WHERE clause، قد يعيد بيانات كثيرة');
      }

      return {
        isValid: errors.length === 0,
        errors: errors,
        warnings: warnings
      };

    } catch (error) {
      return {
        isValid: false,
        errors: [`خطأ في التحقق: ${error}`],
        warnings: warnings
      };
    }
  }

  /**
   * الحصول على معلومات الجداول
   */
  async getTableInfo(tableName: string): Promise<any> {
    try {
      const query = `
        SELECT 
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = '${tableName}'
        ORDER BY ORDINAL_POSITION
      `;

      const result = await this.executeQuery(query, undefined, true);
      
      if (result.success) {
        return {
          tableName: tableName,
          columns: result.data,
          rowCount: await this.getTableRowCount(tableName)
        };
      } else {
        throw new Error(result.error);
      }

    } catch (error) {
      throw new Error(`فشل في الحصول على معلومات الجدول: ${error}`);
    }
  }

  /**
   * الحصول على عدد الصفوف في الجدول
   */
  async getTableRowCount(tableName: string): Promise<number> {
    try {
      const query = `SELECT COUNT(*) as row_count FROM ${tableName}`;
      const result = await this.executeQuery(query, undefined, true);
      
      if (result.success && result.data && result.data.length > 0) {
        return result.data[0].row_count || 0;
      }
      
      return 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * تصدير النتائج إلى CSV
   */
  exportToCSV(data: any[], filename: string): string {
    if (!data || data.length === 0) {
      return '';
    }

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    return csvContent;
  }

  /**
   * إنشاء مفتاح للذاكرة المؤقتة
   */
  private generateCacheKey(query: string, connectionId?: string): string {
    const normalizedQuery = query.trim().toLowerCase();
    return `${connectionId || 'default'}_${Buffer.from(normalizedQuery).toString('base64')}`;
  }

  /**
   * استخراج أسماء الأعمدة من النتائج
   */
  private extractColumns(data: any[]): string[] {
    if (!data || data.length === 0) {
      return [];
    }
    return Object.keys(data[0]);
  }

  /**
   * فحص صحة SQL syntax
   */
  private async checkSQLSyntax(query: string): Promise<{
    isValid: boolean;
    errors: string[];
  }> {
    // فحص أساسي للـ syntax
    const errors: string[] = [];
    
    // فحص الأقواس المتوازنة
    const openParens = (query.match(/\(/g) || []).length;
    const closeParens = (query.match(/\)/g) || []).length;
    
    if (openParens !== closeParens) {
      errors.push('الأقواس غير متوازنة');
    }

    // فحص الكلمات المفتاحية الأساسية
    if (!query.trim().toUpperCase().startsWith('SELECT')) {
      errors.push('يجب أن يبدأ الاستعلام بـ SELECT');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * استخراج أسماء الجداول من الاستعلام
   */
  private extractTableNames(query: string): string[] {
    const tables: string[] = [];
    const fromRegex = /FROM\s+(\w+)/gi;
    const joinRegex = /JOIN\s+(\w+)/gi;
    
    let match;
    while ((match = fromRegex.exec(query)) !== null) {
      tables.push(match[1]);
    }
    
    while ((match = joinRegex.exec(query)) !== null) {
      tables.push(match[1]);
    }
    
    return [...new Set(tables)];
  }

  /**
   * استخراج أسماء الأعمدة من الاستعلام
   */
  private extractColumnNames(query: string): string[] {
    // تنفيذ مبسط - في التطبيق الحقيقي سيكون أكثر تعقيداً
    const columns: string[] = [];
    const selectMatch = query.match(/SELECT\s+(.*?)\s+FROM/i);
    
    if (selectMatch && selectMatch[1] !== '*') {
      const columnList = selectMatch[1].split(',');
      for (const col of columnList) {
        const cleanCol = col.trim().replace(/\s+as\s+\w+/i, '').trim();
        if (cleanCol && !cleanCol.includes('(')) {
          columns.push(cleanCol);
        }
      }
    }
    
    return columns;
  }

  /**
   * فحص وجود الجدول
   */
  private async checkTableExists(tableName: string): Promise<boolean> {
    try {
      const query = `
        SELECT COUNT(*) as table_count 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME = '${tableName}'
      `;
      
      const result = await this.executeQuery(query, undefined, true);
      return result.success && result.data && result.data[0]?.table_count > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * توليد توصيات الأداء
   */
  private generateRecommendations(planData: any[]): string[] {
    const recommendations: string[] = [];
    
    if (!planData || planData.length === 0) {
      return recommendations;
    }

    // تحليل خطة التنفيذ وتوليد التوصيات
    for (const step of planData) {
      if (step.type === 'ALL' || step.Extra?.includes('Using filesort')) {
        recommendations.push('فحص كامل للجدول - يُنصح بإضافة فهرس');
      }
      
      if (step.Extra?.includes('Using temporary')) {
        recommendations.push('استخدام جدول مؤقت - يُنصح بتحسين الاستعلام');
      }
    }

    return recommendations;
  }

  /**
   * مسح الذاكرة المؤقتة
   */
  clearCache(): void {
    this.queryCache.clear();
  }

  /**
   * الحصول على إحصائيات الذاكرة المؤقتة
   */
  getCacheStats(): any {
    return {
      size: this.queryCache.size,
      keys: Array.from(this.queryCache.keys())
    };
  }
}

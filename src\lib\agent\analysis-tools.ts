export interface DataAnalysis {
  summary: {
    totalRows: number;
    totalColumns: number;
    dataTypes: { [column: string]: string };
    nullCounts: { [column: string]: number };
  };
  statistics: {
    numerical: { [column: string]: NumericalStats };
    categorical: { [column: string]: CategoricalStats };
  };
  insights: string[];
  recommendations: string[];
}

export interface NumericalStats {
  count: number;
  mean: number;
  median: number;
  min: number;
  max: number;
  stdDev: number;
  quartiles: {
    q1: number;
    q2: number;
    q3: number;
  };
}

export interface CategoricalStats {
  count: number;
  uniqueValues: number;
  topValues: { value: any; count: number; percentage: number }[];
  distribution: { [value: string]: number };
}

export interface TrendAnalysis {
  trend: 'increasing' | 'decreasing' | 'stable' | 'volatile';
  strength: number; // 0-1
  seasonality: boolean;
  changeRate: number;
  predictions: { period: string; value: number; confidence: number }[];
}

/**
 * أدوات التحليل - تحليل البيانات واستخراج الرؤى
 */
export class AnalysisTools {
  
  /**
   * تحليل شامل للبيانات
   */
  analyzeData(data: any[]): DataAnalysis {
    if (!data || data.length === 0) {
      return this.createEmptyAnalysis();
    }

    const columns = Object.keys(data[0]);
    const summary = this.generateSummary(data, columns);
    const statistics = this.generateStatistics(data, columns);
    const insights = this.generateInsights(data, statistics);
    const recommendations = this.generateRecommendations(statistics, insights);

    return {
      summary,
      statistics,
      insights,
      recommendations
    };
  }

  /**
   * تحليل الاتجاهات الزمنية
   */
  analyzeTrend(data: any[], dateColumn: string, valueColumn: string): TrendAnalysis {
    if (!data || data.length < 2) {
      return {
        trend: 'stable',
        strength: 0,
        seasonality: false,
        changeRate: 0,
        predictions: []
      };
    }

    // ترتيب البيانات حسب التاريخ
    const sortedData = data
      .filter(row => row[dateColumn] && row[valueColumn] !== null)
      .sort((a, b) => new Date(a[dateColumn]).getTime() - new Date(b[dateColumn]).getTime());

    if (sortedData.length < 2) {
      return {
        trend: 'stable',
        strength: 0,
        seasonality: false,
        changeRate: 0,
        predictions: []
      };
    }

    const values = sortedData.map(row => parseFloat(row[valueColumn]) || 0);
    const trend = this.calculateTrend(values);
    const seasonality = this.detectSeasonality(values);
    const changeRate = this.calculateChangeRate(values);
    const predictions = this.generatePredictions(sortedData, dateColumn, valueColumn);

    return {
      trend: trend.direction,
      strength: trend.strength,
      seasonality,
      changeRate,
      predictions
    };
  }

  /**
   * مقارنة بين مجموعات البيانات
   */
  compareDatasets(
    dataset1: any[],
    dataset2: any[],
    valueColumn: string,
    label1: string = 'المجموعة الأولى',
    label2: string = 'المجموعة الثانية'
  ): any {
    const stats1 = this.calculateNumericalStats(dataset1, valueColumn);
    const stats2 = this.calculateNumericalStats(dataset2, valueColumn);

    const comparison = {
      [label1]: stats1,
      [label2]: stats2,
      differences: {
        meanDifference: stats2.mean - stats1.mean,
        medianDifference: stats2.median - stats1.median,
        percentageChange: ((stats2.mean - stats1.mean) / stats1.mean) * 100
      },
      insights: this.generateComparisonInsights(stats1, stats2, label1, label2)
    };

    return comparison;
  }

  /**
   * تحليل التوزيع
   */
  analyzeDistribution(data: any[], column: string): any {
    const values = data
      .map(row => row[column])
      .filter(val => val !== null && val !== undefined);

    if (values.length === 0) {
      return { error: 'لا توجد بيانات صالحة للتحليل' };
    }

    const isNumeric = values.every(val => !isNaN(parseFloat(val)));

    if (isNumeric) {
      return this.analyzeNumericalDistribution(values.map(val => parseFloat(val)));
    } else {
      return this.analyzeCategoricalDistribution(values);
    }
  }

  /**
   * كشف القيم الشاذة
   */
  detectOutliers(data: any[], column: string): any {
    const values = data
      .map(row => parseFloat(row[column]))
      .filter(val => !isNaN(val));

    if (values.length === 0) {
      return { outliers: [], method: 'none' };
    }

    const sorted = values.sort((a, b) => a - b);
    const q1 = this.calculatePercentile(sorted, 25);
    const q3 = this.calculatePercentile(sorted, 75);
    const iqr = q3 - q1;
    const lowerBound = q1 - 1.5 * iqr;
    const upperBound = q3 + 1.5 * iqr;

    const outliers = data
      .map((row, index) => ({ ...row, originalIndex: index }))
      .filter(row => {
        const value = parseFloat(row[column]);
        return !isNaN(value) && (value < lowerBound || value > upperBound);
      });

    return {
      outliers,
      bounds: { lower: lowerBound, upper: upperBound },
      method: 'IQR',
      count: outliers.length,
      percentage: (outliers.length / data.length) * 100
    };
  }

  /**
   * تحليل الارتباط بين المتغيرات
   */
  analyzeCorrelation(data: any[], column1: string, column2: string): any {
    const pairs = data
      .map(row => ({
        x: parseFloat(row[column1]),
        y: parseFloat(row[column2])
      }))
      .filter(pair => !isNaN(pair.x) && !isNaN(pair.y));

    if (pairs.length < 2) {
      return { correlation: 0, strength: 'لا توجد بيانات كافية' };
    }

    const correlation = this.calculateCorrelation(pairs);
    const strength = this.interpretCorrelation(correlation);

    return {
      correlation,
      strength,
      sampleSize: pairs.length,
      interpretation: this.getCorrelationInterpretation(correlation, column1, column2)
    };
  }

  /**
   * توليد ملخص البيانات
   */
  private generateSummary(data: any[], columns: string[]): any {
    const summary = {
      totalRows: data.length,
      totalColumns: columns.length,
      dataTypes: {} as { [column: string]: string },
      nullCounts: {} as { [column: string]: number }
    };

    for (const column of columns) {
      const values = data.map(row => row[column]);
      summary.dataTypes[column] = this.detectDataType(values);
      summary.nullCounts[column] = values.filter(val => val === null || val === undefined || val === '').length;
    }

    return summary;
  }

  /**
   * توليد الإحصائيات
   */
  private generateStatistics(data: any[], columns: string[]): any {
    const statistics = {
      numerical: {} as { [column: string]: NumericalStats },
      categorical: {} as { [column: string]: CategoricalStats }
    };

    for (const column of columns) {
      const values = data.map(row => row[column]).filter(val => val !== null && val !== undefined);
      
      if (this.isNumerical(values)) {
        statistics.numerical[column] = this.calculateNumericalStats(data, column);
      } else {
        statistics.categorical[column] = this.calculateCategoricalStats(data, column);
      }
    }

    return statistics;
  }

  /**
   * حساب الإحصائيات الرقمية
   */
  private calculateNumericalStats(data: any[], column: string): NumericalStats {
    const values = data
      .map(row => parseFloat(row[column]))
      .filter(val => !isNaN(val))
      .sort((a, b) => a - b);

    if (values.length === 0) {
      return {
        count: 0,
        mean: 0,
        median: 0,
        min: 0,
        max: 0,
        stdDev: 0,
        quartiles: { q1: 0, q2: 0, q3: 0 }
      };
    }

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const median = this.calculatePercentile(values, 50);
    const stdDev = Math.sqrt(
      values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    );

    return {
      count: values.length,
      mean: Math.round(mean * 100) / 100,
      median: median,
      min: values[0],
      max: values[values.length - 1],
      stdDev: Math.round(stdDev * 100) / 100,
      quartiles: {
        q1: this.calculatePercentile(values, 25),
        q2: median,
        q3: this.calculatePercentile(values, 75)
      }
    };
  }

  /**
   * حساب الإحصائيات الفئوية
   */
  private calculateCategoricalStats(data: any[], column: string): CategoricalStats {
    const values = data.map(row => row[column]).filter(val => val !== null && val !== undefined);
    const distribution: { [value: string]: number } = {};

    for (const value of values) {
      const key = String(value);
      distribution[key] = (distribution[key] || 0) + 1;
    }

    const sortedEntries = Object.entries(distribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10); // أعلى 10 قيم

    const topValues = sortedEntries.map(([value, count]) => ({
      value,
      count,
      percentage: Math.round((count / values.length) * 100 * 100) / 100
    }));

    return {
      count: values.length,
      uniqueValues: Object.keys(distribution).length,
      topValues,
      distribution
    };
  }

  /**
   * حساب النسبة المئوية
   */
  private calculatePercentile(sortedValues: number[], percentile: number): number {
    const index = (percentile / 100) * (sortedValues.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    
    if (lower === upper) {
      return sortedValues[lower];
    }
    
    const weight = index - lower;
    return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
  }

  /**
   * كشف نوع البيانات
   */
  private detectDataType(values: any[]): string {
    const nonNullValues = values.filter(val => val !== null && val !== undefined && val !== '');
    
    if (nonNullValues.length === 0) return 'unknown';
    
    const sample = nonNullValues.slice(0, 100); // عينة للفحص
    
    if (sample.every(val => !isNaN(parseFloat(val)))) {
      return 'number';
    }
    
    if (sample.every(val => !isNaN(Date.parse(val)))) {
      return 'date';
    }
    
    if (sample.every(val => typeof val === 'boolean' || val === 'true' || val === 'false')) {
      return 'boolean';
    }
    
    return 'string';
  }

  /**
   * فحص ما إذا كانت القيم رقمية
   */
  private isNumerical(values: any[]): boolean {
    const nonNullValues = values.filter(val => val !== null && val !== undefined && val !== '');
    return nonNullValues.length > 0 && nonNullValues.every(val => !isNaN(parseFloat(val)));
  }

  /**
   * حساب الاتجاه
   */
  private calculateTrend(values: number[]): { direction: 'increasing' | 'decreasing' | 'stable' | 'volatile'; strength: number } {
    if (values.length < 2) return { direction: 'stable', strength: 0 };

    const changes = [];
    for (let i = 1; i < values.length; i++) {
      changes.push(values[i] - values[i - 1]);
    }

    const avgChange = changes.reduce((sum, change) => sum + change, 0) / changes.length;
    const absAvgChange = Math.abs(avgChange);
    const maxValue = Math.max(...values);
    const minValue = Math.min(...values);
    const range = maxValue - minValue;

    if (range === 0) return { direction: 'stable', strength: 0 };

    const strength = Math.min(absAvgChange / range, 1);

    if (Math.abs(avgChange) < range * 0.01) {
      return { direction: 'stable', strength };
    }

    const volatility = this.calculateVolatility(changes);
    if (volatility > range * 0.1) {
      return { direction: 'volatile', strength };
    }

    return {
      direction: avgChange > 0 ? 'increasing' : 'decreasing',
      strength
    };
  }

  /**
   * حساب التقلبات
   */
  private calculateVolatility(changes: number[]): number {
    const mean = changes.reduce((sum, change) => sum + change, 0) / changes.length;
    const variance = changes.reduce((sum, change) => sum + Math.pow(change - mean, 2), 0) / changes.length;
    return Math.sqrt(variance);
  }

  /**
   * كشف الموسمية
   */
  private detectSeasonality(values: number[]): boolean {
    // تنفيذ مبسط - في التطبيق الحقيقي سيكون أكثر تعقيداً
    if (values.length < 12) return false;

    // فحص الأنماط المتكررة
    const periods = [7, 12, 24]; // أسبوعي، شهري، سنوي
    
    for (const period of periods) {
      if (values.length >= period * 2) {
        const correlation = this.calculateSeasonalCorrelation(values, period);
        if (correlation > 0.5) return true;
      }
    }

    return false;
  }

  /**
   * حساب الارتباط الموسمي
   */
  private calculateSeasonalCorrelation(values: number[], period: number): number {
    const pairs = [];
    for (let i = period; i < values.length; i++) {
      pairs.push({ x: values[i - period], y: values[i] });
    }
    return this.calculateCorrelation(pairs);
  }

  /**
   * حساب معدل التغيير
   */
  private calculateChangeRate(values: number[]): number {
    if (values.length < 2) return 0;
    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    if (firstValue === 0) return 0;
    return ((lastValue - firstValue) / firstValue) * 100;
  }

  /**
   * توليد التنبؤات
   */
  private generatePredictions(data: any[], dateColumn: string, valueColumn: string): any[] {
    // تنفيذ مبسط للتنبؤ الخطي
    const predictions = [];
    const values = data.map(row => parseFloat(row[valueColumn])).filter(val => !isNaN(val));
    
    if (values.length < 2) return predictions;

    const trend = this.calculateTrend(values);
    const lastValue = values[values.length - 1];
    const avgChange = trend.direction === 'increasing' ? 
      (values[values.length - 1] - values[0]) / values.length :
      trend.direction === 'decreasing' ?
      (values[values.length - 1] - values[0]) / values.length : 0;

    // توليد 3 تنبؤات للفترات القادمة
    for (let i = 1; i <= 3; i++) {
      const predictedValue = lastValue + (avgChange * i);
      const confidence = Math.max(0.3, 1 - (i * 0.2)); // تقل الثقة مع الوقت

      predictions.push({
        period: `الفترة ${i}`,
        value: Math.round(predictedValue * 100) / 100,
        confidence: Math.round(confidence * 100) / 100
      });
    }

    return predictions;
  }

  /**
   * حساب الارتباط
   */
  private calculateCorrelation(pairs: { x: number; y: number }[]): number {
    const n = pairs.length;
    if (n < 2) return 0;

    const sumX = pairs.reduce((sum, pair) => sum + pair.x, 0);
    const sumY = pairs.reduce((sum, pair) => sum + pair.y, 0);
    const sumXY = pairs.reduce((sum, pair) => sum + pair.x * pair.y, 0);
    const sumX2 = pairs.reduce((sum, pair) => sum + pair.x * pair.x, 0);
    const sumY2 = pairs.reduce((sum, pair) => sum + pair.y * pair.y, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

    if (denominator === 0) return 0;
    return numerator / denominator;
  }

  /**
   * تفسير قوة الارتباط
   */
  private interpretCorrelation(correlation: number): string {
    const abs = Math.abs(correlation);
    if (abs >= 0.8) return 'قوي جداً';
    if (abs >= 0.6) return 'قوي';
    if (abs >= 0.4) return 'متوسط';
    if (abs >= 0.2) return 'ضعيف';
    return 'ضعيف جداً أو لا يوجد';
  }

  /**
   * الحصول على تفسير الارتباط
   */
  private getCorrelationInterpretation(correlation: number, column1: string, column2: string): string {
    const direction = correlation > 0 ? 'طردي' : 'عكسي';
    const strength = this.interpretCorrelation(correlation);
    
    return `يوجد ارتباط ${direction} ${strength} بين ${column1} و ${column2}`;
  }

  /**
   * توليد الرؤى
   */
  private generateInsights(data: any[], statistics: any): string[] {
    const insights: string[] = [];
    
    // رؤى حول البيانات الرقمية
    for (const [column, stats] of Object.entries(statistics.numerical)) {
      const numStats = stats as NumericalStats;
      if (numStats.stdDev > numStats.mean) {
        insights.push(`${column}: البيانات متقلبة جداً (الانحراف المعياري أكبر من المتوسط)`);
      }
      
      if (numStats.max > numStats.mean * 10) {
        insights.push(`${column}: توجد قيم عالية جداً قد تكون شاذة`);
      }
    }

    // رؤى حول البيانات الفئوية
    for (const [column, stats] of Object.entries(statistics.categorical)) {
      const catStats = stats as CategoricalStats;
      if (catStats.topValues.length > 0 && catStats.topValues[0].percentage > 80) {
        insights.push(`${column}: قيمة واحدة تهيمن على البيانات (${catStats.topValues[0].percentage}%)`);
      }
    }

    return insights;
  }

  /**
   * توليد التوصيات
   */
  private generateRecommendations(statistics: any, insights: string[]): string[] {
    const recommendations: string[] = [];
    
    if (insights.some(insight => insight.includes('متقلبة'))) {
      recommendations.push('يُنصح بفحص البيانات للتأكد من صحتها وإزالة القيم الشاذة');
    }
    
    if (insights.some(insight => insight.includes('تهيمن'))) {
      recommendations.push('قد تحتاج إلى تجميع القيم النادرة أو إعادة تصنيف البيانات');
    }

    return recommendations;
  }

  /**
   * توليد رؤى المقارنة
   */
  private generateComparisonInsights(stats1: NumericalStats, stats2: NumericalStats, label1: string, label2: string): string[] {
    const insights: string[] = [];
    
    const meanDiff = ((stats2.mean - stats1.mean) / stats1.mean) * 100;
    if (Math.abs(meanDiff) > 10) {
      const direction = meanDiff > 0 ? 'أعلى' : 'أقل';
      insights.push(`${label2} ${direction} من ${label1} بنسبة ${Math.abs(meanDiff).toFixed(1)}%`);
    }

    if (stats2.stdDev > stats1.stdDev * 1.5) {
      insights.push(`${label2} أكثر تقلباً من ${label1}`);
    }

    return insights;
  }

  /**
   * تحليل التوزيع الرقمي
   */
  private analyzeNumericalDistribution(values: number[]): any {
    const sorted = values.sort((a, b) => a - b);
    const stats = {
      count: values.length,
      mean: values.reduce((sum, val) => sum + val, 0) / values.length,
      median: this.calculatePercentile(sorted, 50),
      mode: this.calculateMode(values),
      skewness: this.calculateSkewness(values),
      kurtosis: this.calculateKurtosis(values)
    };

    return {
      type: 'numerical',
      statistics: stats,
      distribution: this.createHistogram(sorted),
      interpretation: this.interpretDistribution(stats)
    };
  }

  /**
   * تحليل التوزيع الفئوي
   */
  private analyzeCategoricalDistribution(values: any[]): any {
    const distribution: { [key: string]: number } = {};
    
    for (const value of values) {
      const key = String(value);
      distribution[key] = (distribution[key] || 0) + 1;
    }

    const sortedEntries = Object.entries(distribution)
      .sort(([,a], [,b]) => b - a);

    return {
      type: 'categorical',
      distribution: distribution,
      topValues: sortedEntries.slice(0, 10),
      uniqueCount: Object.keys(distribution).length,
      entropy: this.calculateEntropy(Object.values(distribution))
    };
  }

  /**
   * حساب المنوال
   */
  private calculateMode(values: number[]): number | null {
    const frequency: { [key: number]: number } = {};
    
    for (const value of values) {
      frequency[value] = (frequency[value] || 0) + 1;
    }

    let maxFreq = 0;
    let mode = null;
    
    for (const [value, freq] of Object.entries(frequency)) {
      if (freq > maxFreq) {
        maxFreq = freq;
        mode = parseFloat(value);
      }
    }

    return maxFreq > 1 ? mode : null;
  }

  /**
   * حساب الالتواء
   */
  private calculateSkewness(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    if (stdDev === 0) return 0;
    
    const skewness = values.reduce((sum, val) => sum + Math.pow((val - mean) / stdDev, 3), 0) / values.length;
    return skewness;
  }

  /**
   * حساب التفرطح
   */
  private calculateKurtosis(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    if (stdDev === 0) return 0;
    
    const kurtosis = values.reduce((sum, val) => sum + Math.pow((val - mean) / stdDev, 4), 0) / values.length;
    return kurtosis - 3; // تصحيح للتوزيع الطبيعي
  }

  /**
   * إنشاء هيستوجرام
   */
  private createHistogram(sortedValues: number[], bins: number = 10): any {
    const min = sortedValues[0];
    const max = sortedValues[sortedValues.length - 1];
    const binWidth = (max - min) / bins;
    
    const histogram = Array(bins).fill(0);
    
    for (const value of sortedValues) {
      const binIndex = Math.min(Math.floor((value - min) / binWidth), bins - 1);
      histogram[binIndex]++;
    }

    return {
      bins: histogram,
      binWidth: binWidth,
      range: { min, max }
    };
  }

  /**
   * تفسير التوزيع
   */
  private interpretDistribution(stats: any): string[] {
    const interpretations: string[] = [];
    
    if (Math.abs(stats.skewness) > 1) {
      const direction = stats.skewness > 0 ? 'يميناً' : 'يساراً';
      interpretations.push(`التوزيع منحرف ${direction}`);
    }
    
    if (stats.kurtosis > 1) {
      interpretations.push('التوزيع مدبب (قيم متركزة حول المتوسط)');
    } else if (stats.kurtosis < -1) {
      interpretations.push('التوزيع مسطح (قيم منتشرة)');
    }

    return interpretations;
  }

  /**
   * حساب الإنتروبيا
   */
  private calculateEntropy(frequencies: number[]): number {
    const total = frequencies.reduce((sum, freq) => sum + freq, 0);
    if (total === 0) return 0;
    
    let entropy = 0;
    for (const freq of frequencies) {
      if (freq > 0) {
        const probability = freq / total;
        entropy -= probability * Math.log2(probability);
      }
    }
    
    return entropy;
  }

  /**
   * إنشاء تحليل فارغ
   */
  private createEmptyAnalysis(): DataAnalysis {
    return {
      summary: {
        totalRows: 0,
        totalColumns: 0,
        dataTypes: {},
        nullCounts: {}
      },
      statistics: {
        numerical: {},
        categorical: {}
      },
      insights: ['لا توجد بيانات للتحليل'],
      recommendations: ['يرجى التأكد من وجود بيانات صالحة']
    };
  }
}

import { IntentClassifier } from './intent-classifier';

/**
 * اختبار مصنف النوايا
 */
async function testIntentClassifier() {
  console.log('🧪 بدء اختبار مصنف النوايا...\n');
  
  const classifier = new IntentClassifier();
  
  // اختبارات مختلفة للنوايا
  const testQueries = [
    {
      query: 'إجمالي المبيعات اليوم',
      expectedIntent: 'sales_total',
      description: 'استعلام بسيط عن المبيعات'
    },
    {
      query: 'أكثر المنتجات مبيعاً هذا الشهر',
      expectedIntent: 'top_products',
      description: 'استعلام عن أفضل المنتجات'
    },
    {
      query: 'أفضل العملاء في فرع الرياض',
      expectedIntent: 'customer_analysis',
      description: 'تحليل العملاء مع تحديد الفرع'
    },
    {
      query: 'حالة المخزون للمنتج ABC',
      expectedIntent: 'inventory_status',
      description: 'استعلام عن المخزون'
    },
    {
      query: 'مشتريات من مورد الشركة الذهبية',
      expectedIntent: 'purchase_analysis',
      description: 'تحليل المشتريات من مورد محدد'
    },
    {
      query: 'أرباح الشهر الماضي',
      expectedIntent: 'financial_reports',
      description: 'تقرير مالي'
    },
    {
      query: 'مقارنة أداء الفروع',
      expectedIntent: 'branch_performance',
      description: 'مقارنة الفروع'
    },
    {
      query: 'مبيعات شهرية لهذه السنة',
      expectedIntent: 'time_analysis',
      description: 'تحليل زمني'
    },
    {
      query: 'حركة صنف الهواتف الذكية',
      expectedIntent: 'product_movement',
      description: 'تتبع حركة منتج'
    },
    {
      query: 'المنتجات المرتجعة هذا الأسبوع',
      expectedIntent: 'returns_analysis',
      description: 'تحليل المرتجعات'
    }
  ];

  let successCount = 0;
  let totalTests = testQueries.length;

  for (const test of testQueries) {
    console.log(`📝 اختبار: ${test.description}`);
    console.log(`   الاستعلام: "${test.query}"`);
    
    try {
      const result = await classifier.classifyIntent(test.query);
      
      if (result) {
        console.log(`   ✅ النية المكتشفة: ${result.intent.id} (ثقة: ${(result.confidence * 100).toFixed(1)}%)`);
        console.log(`   📊 الكيانات المستخرجة: ${result.entities.length}`);
        
        if (result.entities.length > 0) {
          result.entities.forEach(entity => {
            console.log(`      - ${entity.type}: "${entity.value}" (ثقة: ${(entity.confidence * 100).toFixed(1)}%)`);
          });
        }
        
        if (result.intent.id === test.expectedIntent) {
          console.log(`   🎯 النتيجة: صحيحة`);
          successCount++;
        } else {
          console.log(`   ❌ النتيجة: خاطئة (متوقع: ${test.expectedIntent})`);
        }
      } else {
        console.log(`   ❌ لم يتم التعرف على النية`);
      }
      
    } catch (error) {
      console.log(`   💥 خطأ: ${error}`);
    }
    
    console.log('   ' + '─'.repeat(60));
  }

  console.log(`\n📊 نتائج الاختبار:`);
  console.log(`   ✅ نجح: ${successCount}/${totalTests} (${((successCount/totalTests) * 100).toFixed(1)}%)`);
  console.log(`   ❌ فشل: ${totalTests - successCount}/${totalTests}`);
  
  // اختبار إضافي: استعلامات غامضة
  console.log(`\n🔍 اختبار الاستعلامات الغامضة:`);
  
  const ambiguousQueries = [
    'بيانات',
    'معلومات',
    'تقرير',
    'أرقام',
    'إحصائيات'
  ];

  for (const query of ambiguousQueries) {
    const result = await classifier.classifyIntent(query);
    if (result) {
      console.log(`   "${query}" → ${result.intent.id} (ثقة: ${(result.confidence * 100).toFixed(1)}%)`);
    } else {
      console.log(`   "${query}" → لم يتم التعرف على النية ✅`);
    }
  }
}

/**
 * اختبار أداء المصنف
 */
async function performanceTest() {
  console.log('\n⚡ اختبار الأداء...');
  
  const classifier = new IntentClassifier();
  const testQuery = 'إجمالي المبيعات اليوم';
  const iterations = 1000;
  
  const startTime = Date.now();
  
  for (let i = 0; i < iterations; i++) {
    await classifier.classifyIntent(testQuery);
  }
  
  const endTime = Date.now();
  const totalTime = endTime - startTime;
  const avgTime = totalTime / iterations;
  
  console.log(`   📊 ${iterations} استعلام في ${totalTime}ms`);
  console.log(`   ⚡ متوسط الوقت: ${avgTime.toFixed(2)}ms لكل استعلام`);
  console.log(`   🚀 المعدل: ${(1000 / avgTime).toFixed(0)} استعلام/ثانية`);
}

// تشغيل الاختبارات
if (require.main === module) {
  (async () => {
    await testIntentClassifier();
    await performanceTest();
  })();
}

export { testIntentClassifier, performanceTest };

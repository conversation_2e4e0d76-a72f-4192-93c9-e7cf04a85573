{"databaseName": "SalesTempDB", "databaseType": "mssql", "tables": [{"name": "tbltemp_Inv_MainInvoice", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SupplierName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "InvoiceID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DetailsID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "EnterTime", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "TotalAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MainUnitQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "MainUnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 20}, {"name": "MainUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "MCAmount", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "MainUnitBonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "ExchangePrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "TotalAmountByCurrencyInvetory", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "NewSubItemEntryID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_Inv_MainInvoice", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 908}, {"name": "tbltemp_ItemsMain", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RowVersion", "type": "timestamp", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "DocumentID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethodID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Discount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Notes", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255, "precision": null, "scale": null}, {"name": "UserID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheYear", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 10, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ClientName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UserName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "BranchName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CategoryID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CategoryName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CategoryNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "ItemTypeID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemType", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "ReorderPoint", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 4}, {"name": "ISActive", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ISExpiry", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ExpiryPoint", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountFatherNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Account<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CostCenterNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Barcode", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitRank", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "PackageQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "BarcodeID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SerialNumber", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ItemD<PERSON>unt", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "McItemDiscountCurrencyMain", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "Mc<PERSON>temDiscount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "Amount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmountCurrencyMain", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "AccountID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "PackageUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "PackageUnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "NextParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangePrice", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExchangePriceCurrencyInvetory", "type": "decimal", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_ItemsMain", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 0}], "relationships": [], "extractedAt": "2025-07-22T04:01:01.959Z", "version": "1.0", "tableDescriptions": [{"tableName": "tbltemp_Inv_MainInvoice", "description": "جدول tbltemp_Inv_MainInvoice يستخدم لتخزين بيانات الفواتير الرئيسية بشكل مؤقت، حيث يحتوي على معلومات تفصيلية حول الفواتير مثل رقم الفاتورة، اسم المورد، تاريخ الإدخال، تفاصيل البند، السعر الوحدة، الكمية، والمبلغ الإجمالي. هذا الجدول يمكن استخدامه في عمليات التحليل والتدقيق قبل إدخال البيانات بشكل نهائي في قاعدة البيانات.", "columnDescriptions": {"ID": "رقم التعريف الفريد لكل سجل في الجدول", "DocumentName": "اسم المستند المرتبط بالفاتورة", "RecordID": "رقم التعريف الفريد للسجل الرئيسي المرتبط بالفاتورة", "TheNumber": "رقم الفاتورة، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "SupplierName": "اسم المورد، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "InvoiceID": "رقم التعريف الفريد للفاتورة", "DetailsID": "رقم التعريف الفريد لتفاصيل البند في الفاتورة", "TheDate": "تاريخ الفاتورة، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "CurrencyID": "رقم التعريف الفريد للعملة المستخدمة في الفاتورة، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "TheMethod": "طريقة الدفع، يمكن أن تكون فارغة إذا لم يتم تحديدها بعد", "EnterTime": "وقت إدخال السجل في الجدول، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "ItemID": "رقم التعريف الفريد للبند في الفاتورة، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "UnitID": "رقم التعريف الفريد للوحدة المستخدمة في القياس، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "UnitPrice": "سعر الوحدة للبند، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "Quantity": "كمية البند، يمكن أن تكون فارغة إذا لم يتم تحديدها بعد", "Bonus": "البونص أو الخصم المقدم، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "TotalAmount": "المبلغ الإجمالي للبند، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "MainUnitQuantity": "الكمية الرئيسية للبند، يمكن أن تكون فارغة إذا لم يتم تحديدها بعد", "MainUnitPrice": "سعر الوحدة الرئيسية للبند، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "MainUnitID": "رقم التعريف الفريد للوحدة الرئيسية المستخدمة في القياس، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "StoreID": "رقم التعريف الفريد للمخزن، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "BranchID": "رقم التعريف الفريد للفرع، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "ExchangeFactor": "عامل الصرف بين الوحدات المختلفة", "ClientID": "رقم التعريف الفريد للعميل، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "MCAmount": "المبلغ الإجمالي بالعملة المحلية، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "ExpiryDate": "تاريخ انتهاء صلاحية البند، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "MainUnitBonus": "البونص أو الخصم المقدم للوحدة الرئيسية، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "ExchangePrice": "سعر الصرف بين العملات المختلفة، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "DistributorID": "رقم التعريف الفريد للموزع، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "DistributorName": "اسم الموزع، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "CostCenterID": "رقم التعريف الفريد لمركز التكلفة، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "CostCenterName": "اسم مركز التكلفة، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "TotalAmountByCurrencyInvetory": "المبلغ الإجمالي بالعملة المخزنية، يمكن أن يكون فارغًا إذا لم يتم تحديده بعد", "NewSubItemEntryID": "رقم التعريف الفريد للبند الفرعي الجديد"}, "analyticalValue": "الجدول يوفر قيمة تحليلية كبيرة في فهم تفاصيل الفواتير وعمليات الشراء، مما يساعد في إدارة المخزون والتوريدات بشكل أفضل. يمكن استخدامه لتحليل الأنماط في شراء البضائع، تقييم الموردين، ومتابعة التكاليف.", "sqlExamples": [{"query": "SELECT SupplierName, SUM(TotalAmount) AS TotalSpent FROM tbltemp_Inv_MainInvoice GROUP BY SupplierName;", "explanation": "هذا الاستعلام يجمع المبالغ الإجمالية المدفوعة لكل مورد، مما يساعد في تحليل الإنفاق حسب الموردين."}, {"query": "SELECT TheDate, COUNT(*) AS NumberOfInvoices FROM tbltemp_Inv_MainInvoice WHERE TheDate IS NOT NULL GROUP BY TheDate ORDER BY TheDate DESC;", "explanation": "هذا الاستعلام يعرض عدد الفواتير المدخلة لكل تاريخ، مما يساعد في فهم الأنماط الزمنية لعمليات الشراء."}, {"query": "SELECT ItemID, SUM(Quantity) AS TotalQuantity FROM tbltemp_Inv_MainInvoice GROUP BY ItemID ORDER BY TotalQuantity DESC;", "explanation": "هذا الاستعلام يجمع الكمية الإجمالية لكل بند، مما يساعد في تحديد البضائع الأكثر طلبًا."}], "intelligentAnalysis": "الجدول يحتوي على معلومات تفصيلية حول الفواتير والبضائع، مما يسمح بإجراء تحليلات متقدمة مثل تحليل الإنفاق حسب الموردين، تحليل الأنماط الزمنية لعمليات الشراء، وتقييم كميات البضائع الأكثر طلبًا. كما يمكن استخدامه لتحليل التكاليف حسب العملة ومركز التكلفة.", "purpose": "الغرض الأساسي من الجدول هو تخزين بيانات الفواتير الرئيسية بشكل مؤقت قبل إدخالها بشكل نهائي في قاعدة البيانات، مما يساعد في التحقق من صحة البيانات وتنظيمها.", "domain": "تجاري", "businessContext": "يستخدم الجدول في سياق الأعمال التجارية لإدارة عمليات الشراء والمخزون، حيث يوفر معلومات تفصيلية عن الفواتير والبضائع والتكاليف.", "keyFields": ["ID", "InvoiceID", "DetailsID", "RecordID", "ItemID", "SupplierName", "TotalAmount"], "relatedTables": [], "limitations": "الجدول لا يحتوي على مفاتيح خارجية، مما قد يحد من قدرة التكامل مع جداول أخرى. كما أن بعض الحقول يمكن أن تكون فارغة، مما قد يؤثر على دقة التحليلات إذا لم يتم ملؤها بشكل صحيح.", "generatedAt": "2025-07-22T04:02:11.087Z"}, {"tableName": "tbltemp_ItemsMain", "description": "جدول tbltemp_ItemsMain يحتوي على بيانات تفصيلية عن العناصر والمواد في نظام إدارة المخزون والمستودعات. يوفر هذا الجدول معلومات متنوعة تتعلق بالعناصر، مثل معلومات العميل، الموزع، العملة، الطريقة، المستخدم، الفرع، الفئة، البند، الوحدة، الحساب، المركز التكلفة، الباركود، السعر، الخصم، الكمية، المكافأة، تاريخ الانتهاء، المبلغ، وغيرها من المعلومات المالية والتجارية.", "columnDescriptions": {"ID": "معرّف فريد للبند في الجدول", "ParentID": "معرّف البند الأبوة (يمكن أن يكون فارغًا)", "RowVersion": "ختم زمني لنسخة السجل (يمكن أن يكون فارغًا)", "DocumentID": "معرّف الوثيقة المرتبطة بالبند", "RecordNumber": "رقم السجل", "RecordID": "معرّف السجل", "TheDate": "تاريخ السجل", "ClientID": "معرّف العميل (يمكن أن يكون فارغًا)", "DistributorID": "معرّف الموزع (يمكن أن يكون فارغًا)", "CurrencyID": "معرّف العملة (يمكن أن يكون فارغًا)", "TheMethodID": "معرّف الطريقة (يمكن أن يكون فارغًا)", "Discount": "قيمة الخصم (يمكن أن تكون فارغة)", "Notes": "ملاحظات إضافية (يمكن أن تكون فارغة)", "UserID": "معرّف المستخدم الذي قام بإدخال السجل", "BranchID": "معرّف الفرع", "TheYear": "السنة (يمكن أن تكون فارغة)", "DocumentName": "اسم الوثيقة", "TheNumber": "الرقم (يمكن أن يكون فارغًا)", "ClientName": "اسم العميل (يمكن أن يكون فارغًا)", "DistributorName": "اسم الموزع (يمكن أن يكون فارغًا)", "CurrencyName": "اسم العملة", "TheMethod": "اسم الطريقة (يمكن أن يكون فارغًا)", "UserName": "اسم المستخدم", "BranchName": "اسم الفرع", "CategoryID": "معرّف الفئة (يمكن أن يكون فارغًا)", "FatherNumber": "رقم الأب (يمكن أن يكون فارغًا)", "CategoryName": "اسم الفئة (يمكن أن يكون فارغًا)", "CategoryNumber": "رقم الفئة (يمكن أن يكون فارغًا)", "ItemID": "معرّف البند (يمكن أن يكون فارغًا)", "UnitID": "معرّف الوحدة (يمكن أن يكون فارغًا)", "ItemNumber": "رق<PERSON> البند", "ItemName": "اسم البند (يمكن أن يكون فارغًا)", "ItemTypeID": "معرّف نوع البند", "ItemType": "نوع البند (يمكن أن يكون فارغًا)", "ReorderPoint": "نقطة إعادة الطلب (يمكن أن تكون فارغة)", "ISActive": "حالة النشاط (يمكن أن تكون فارغة)", "ISExpiry": "حالة الانتهاء (يمكن أن تكون فارغة)", "ExpiryPoint": "نقطة الانتهاء (يمكن أن تكون فارغة)", "UnitName": "اسم الوحدة (يمكن أن يكون فارغًا)", "AccountFatherNumber": "رقم الحسا<PERSON> الأب (يمكن أن يكون فارغًا)", "AccountName": "اسم الحساب (يمكن أن يكون فارغًا)", "AccountNumber": "رقم الحساب (يمكن أن يكون فارغًا)", "CostCenterID": "معرّف المركز التكلفة (يمكن أن يكون فارغًا)", "CostCenterName": "اسم المركز التكلفة (يمكن أن يكون فارغًا)", "CostCenterNumber": "رقم المركز التكلفة (يمكن أن يكون فارغًا)", "Barcode": "الباركود (يمكن أن يكون فارغًا)", "UnitRank": "رتبة الوحدة (يمكن أن تكون فارغة)", "ExchangeFactor": "عا<PERSON><PERSON> التبادل", "PackageQuantity": "كمية الحزمة (يمكن أن تكون فارغة)", "BarcodeID": "معرّف الباركود (يمكن أن يكون فارغًا)", "SerialNumber": "رقم التسلسل (يمكن أن يكون فارغًا)", "UnitPrice": "سعر الوحدة (يمكن أن يكون فارغًا)", "ItemDiscount": "خصم البند (يمكن أن يكون فارغًا)", "McItemDiscountCurrencyMain": "خصم البند بالعملة الرئيسية (يمكن أن يكون فارغًا)", "McItemDiscount": "خصم البند (يمكن أن يكون فارغًا)", "Quantity": "الكمية (يمكن أن تكون فارغة)", "Bonus": "المكافأة (يمكن أن تكون فارغة)", "ExpiryDate": "تاريخ الانتهاء (يمكن أن يكون فارغًا)", "Amount": "المبلغ (يمكن أن يكون فارغًا)", "MCAmount": "المبلغ (يمكن أن يكون فارغًا)", "MCAmountCurrencyMain": "المبلغ بالعملة الرئيسية (يمكن أن يكون فارغًا)", "AccountID": "معرّف الحساب (يمكن أن يكون فارغًا)", "StoreID": "معرّف المستودع (يمكن أن يكون فارغًا)", "StoreName": "اسم المستودع (يمكن أن يكون فارغًا)", "PackageUnitID": "معرّف وحدة الحزمة (يمكن أن يكون فارغًا)", "PackageUnitName": "اسم وحدة الحزمة (يمكن أن يكون فارغًا)", "NextParentID": "معرّف الأب التالي (يمكن أن يكون فارغًا)", "ExchangePrice": "سعر التبادل", "ExchangePriceCurrencyInvetory": "سعر التبادل بالعملة المخزونية"}, "analyticalValue": "الجدول يوفر قيمة تحليلية وإحصائية كبيرة في مجال إدارة المخزون والمستودعات، حيث يمكن استخدامه لتحليل الأنماط الاستهلاكية، توقع الطلب، إدارة المخزون، تحليل الأداء المالي، ومراقبة جودة المنتجات. يمكن استخدام البيانات الموجودة في الجدول لتحسين عمليات الشراء، التسعير، والتسويق.", "sqlExamples": [{"query": "SELECT ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalAmount FROM tbltemp_ItemsMain GROUP BY ItemName ORDER BY TotalQuantity DESC;", "explanation": "هذا الاستعلام يجمع الكمية والمبلغ الإجمالي لكل بند ويرتّبها حسب الكمية الإجمالية في تناقص. يمكن استخدامه لتحديد الأصناف الأكثر مبيعًا وتحليل الأداء المالي لكل بند."}, {"query": "SELECT BranchName, COUNT(*) AS TotalItems FROM tbltemp_ItemsMain GROUP BY BranchName ORDER BY TotalItems DESC;", "explanation": "هذا الاستعلام يحسب عدد الأصناف في كل فرع ويرتّبها حسب العدد في تناقص. يمكن استخدامه لتقييم توزيع الأصناف بين الفروع وتحديد الفروع التي تحتاج إلى مزيد من الاهتمام."}, {"query": "SELECT TheDate, SUM(Amount) AS DailySales FROM tbltemp_ItemsMain WHERE TheDate BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY TheDate ORDER BY TheDate;", "explanation": "هذا الاستعلام يحسب المبيعات اليومية خلال عام 2023 ويرتّبها حسب التاريخ. يمكن استخدامه لتحليل الأنماط الزمنية في المبيعات وتحديد الفترات ذات الأداء العالي والمنخفض."}], "intelligentAnalysis": "الجدول يحتوي على بيانات متنوعة ومفصلة تتعلق بالعناصر والمواد، مما يسمح بإجراء تحليلات ذكية ومتعددة الأبعاد. يمكن استخدام البيانات لتحليل الأنماط الاستهلاكية، توقع الطلب، إدارة المخزون، تحليل الأداء المالي، ومراقبة جودة المنتجات. كما يمكن استخدام البيانات لتحسين عمليات الشراء، التسعير، والتسويق. العلاقات بين الحقول مثل ParentID و CategoryID يمكن أن تساعد في فهم الهيكل التنظيمي للعناصر والمواد.", "purpose": "الغرض الأساسي من الجدول هو تخزين وتنظيم بيانات العناصر والمواد في نظام إدارة المخزون والمستودعات، مما يسهل عملية إدارة المخزون، تحليل الأداء المالي، وتتبع حركة البضائع.", "domain": "تجاري", "businessContext": "السياق التجاري للجدول يشمل إدارة المخزون والمستودعات، حيث يتم استخدام البيانات لتحسين عمليات الشراء، التسعير، والتسويق. يمكن استخدام الجدول أيضًا في تحليل الأداء المالي وتوقع الطلب.", "keyFields": ["ID", "DocumentID", "ItemID", "BranchID", "StoreID", "TheDate"], "relatedTables": [], "limitations": "الجدول لا يحتوي على مفاتيح خارجية، مما قد يحد من قدرة النظام على التكامل مع جداول أخرى. كما أن بعض الحقول يمكن أن تكون فارغة، مما قد يقلل من دقة التحليلات وال إحصائيات.", "generatedAt": "2025-07-22T04:03:03.381Z"}], "embeddings": {"tbltemp_Inv_MainInvoice": [], "tbltemp_ItemsMain": []}}
import OpenAI from 'openai';

/**
 * عميل OpenRouter للتعامل مع نماذج مختلفة
 */
export class OpenRouterClient {
  private client: OpenAI;
  private static instance: OpenRouterClient | null = null;

  constructor() {
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      throw new Error('OPENROUTER_API_KEY غير موجود في متغيرات البيئة');
    }

    this.client = new OpenAI({
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: apiKey,
      defaultHeaders: {
        "HTTP-Referer": process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
        "X-Title": "AI Database Agent",
      },
    });
  }

  static getInstance(): OpenRouterClient {
    if (!OpenRouterClient.instance) {
      OpenRouterClient.instance = new OpenRouterClient();
    }
    return OpenRouterClient.instance;
  }

  /**
   * توليد وصف للجدول باستخدام OpenRouter
   */
  async generateTableDescription(
    tableName: string,
    columns: Array<{name: string, type: string, nullable: boolean}>,
    foreignKeys: Array<{columnName: string, referencedTable: string}>,
    sampleData?: unknown[]
  ): Promise<{
    description: string;
    columnDescriptions: { [columnName: string]: string };
    analyticalValue: string;
    sqlExamples: Array<{
      query: string;
      explanation: string;
    }>;
    intelligentAnalysis: string;
    purpose: string;
    domain: string;
    businessContext: string;
    keyFields: string[];
    relatedTables: string[];
    limitations: string;
  }> {
    const prompt = this.buildTableAnalysisPrompt(tableName, columns, foreignKeys, sampleData);

    try {
      const completion = await this.client.chat.completions.create({
        model: "qwen/qwen-2.5-72b-instruct",
        messages: [
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 4000,
      });

      const content = completion.choices[0]?.message?.content;
      if (!content) {
        throw new Error('لم يتم الحصول على رد من OpenRouter');
      }

      return this.parseTableAnalysisResponse(content);
    } catch (error) {
      console.error('خطأ في توليد وصف الجدول:', error);
      throw error;
    }
  }

  /**
   * توليد استعلام SQL باستخدام OpenRouter
   */
  async generateSQLQuery(
    question: string,
    tablesInfo: Array<{
      name: string;
      description: string;
      columns: Array<{name: string, type: string}>;
      relationships: Array<{toTable: string, fromColumn: string, toColumn: string}>;
    }>,
    databaseType: 'mysql' | 'mssql' = 'mysql'
  ): Promise<{
    query: string;
    explanation: string;
    confidence: number;
    relevantTables: string[];
  }> {
    const prompt = this.buildSQLGenerationPrompt(question, tablesInfo, databaseType);

    try {
      const completion = await this.client.chat.completions.create({
        model: "qwen/qwen-2.5-72b-instruct",
        messages: [
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000,
      });

      const content = completion.choices[0]?.message?.content;
      if (!content) {
        throw new Error('لم يتم الحصول على رد من OpenRouter');
      }

      return this.parseSQLResponse(content);
    } catch (error) {
      console.error('خطأ في توليد استعلام SQL:', error);
      throw error;
    }
  }

  /**
   * توليد محتوى عام باستخدام OpenRouter
   */
  async generateContent(prompt: string): Promise<string> {
    try {
      const completion = await this.client.chat.completions.create({
        model: "qwen/qwen-2.5-72b-instruct",
        messages: [
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
      });

      const content = completion.choices[0]?.message?.content;
      if (!content) {
        throw new Error('لم يتم الحصول على رد من OpenRouter');
      }

      return content;
    } catch (error) {
      console.error('خطأ في توليد المحتوى:', error);
      throw error;
    }
  }

  /**
   * بناء prompt لتحليل الجدول
   */
  private buildTableAnalysisPrompt(
    tableName: string,
    columns: Array<{name: string, type: string, nullable: boolean}>,
    foreignKeys: Array<{columnName: string, referencedTable: string}>,
    sampleData?: unknown[]
  ): string {
    const columnsText = columns.map(col => 
      `- ${col.name} (${col.type})${col.nullable ? ' - يمكن أن يكون فارغ' : ''}`
    ).join('\n');

    const foreignKeysText = foreignKeys.length > 0 
      ? foreignKeys.map(fk => `- ${fk.columnName} يشير إلى ${fk.referencedTable}`).join('\n')
      : 'لا توجد مفاتيح خارجية';

    const sampleDataText = sampleData && sampleData.length > 0
      ? `\n\nبيانات عينة:\n${JSON.stringify(sampleData.slice(0, 3), null, 2)}`
      : '';

    return `
🔍 **مهمة: تحليل ذكي شامل لجدول قاعدة البيانات**

**اسم الجدول:** ${tableName}

**الأعمدة:**
${columnsText}

**المفاتيح الخارجية:**
${foreignKeysText}
${sampleDataText}

**المطلوب:** تحليل شامل وذكي للجدول وإرجاع النتيجة بصيغة JSON صحيحة تحتوي على:

\`\`\`json
{
  "description": "وصف مفصل ودقيق لغرض الجدول ووظيفته",
  "columnDescriptions": {
    "column_name": "وصف دقيق لكل عمود ووظيفته"
  },
  "analyticalValue": "القيمة التحليلية والإحصائية للجدول",
  "sqlExamples": [
    {
      "query": "استعلام SQL مفيد",
      "explanation": "شرح الاستعلام وفائدته"
    }
  ],
  "intelligentAnalysis": "تحليل ذكي للبيانات والعلاقات",
  "purpose": "الغرض الأساسي من الجدول",
  "domain": "المجال أو النطاق (مثل: طبي، تعليمي، تجاري)",
  "businessContext": "السياق التجاري أو المؤسسي",
  "keyFields": ["أهم الحقول في الجدول"],
  "relatedTables": ["الجداول المرتبطة"],
  "limitations": "القيود أو المحددات المحتملة"
}
\`\`\`

**ملاحظات مهمة:**
- استخدم اللغة العربية في جميع الأوصاف
- كن دقيقاً ومفصلاً في التحليل
- أرجع JSON صحيح فقط بدون أي نص إضافي
`;
  }

  /**
   * بناء prompt لتوليد استعلام SQL
   */
  private buildSQLGenerationPrompt(
    question: string,
    tablesInfo: Array<{
      name: string;
      description: string;
      columns: Array<{name: string, type: string}>;
      relationships: Array<{toTable: string, fromColumn: string, toColumn: string}>;
    }>,
    databaseType: 'mysql' | 'mssql'
  ): string {
    const tablesText = tablesInfo.map(table => {
      const columnsText = table.columns.map(col => `  - ${col.name} (${col.type})`).join('\n');
      const relationshipsText = table.relationships.length > 0
        ? table.relationships.map(rel => `  - ${rel.fromColumn} -> ${rel.toTable}.${rel.toColumn}`).join('\n')
        : '  - لا توجد علاقات';

      return `**${table.name}:**
${table.description}
الأعمدة:
${columnsText}
العلاقات:
${relationshipsText}`;
    }).join('\n\n');

    return `
🎯 **مهمة: توليد استعلام SQL دقيق**

**السؤال:** ${question}

**نوع قاعدة البيانات:** ${databaseType.toUpperCase()}

**الجداول المتاحة:**
${tablesText}

**المطلوب:** إنشاء استعلام SQL دقيق وإرجاع النتيجة بصيغة JSON:

\`\`\`json
{
  "query": "استعلام SQL دقيق ومحسن",
  "explanation": "شرح مفصل للاستعلام وكيفية عمله",
  "confidence": 95,
  "relevantTables": ["أسماء الجداول المستخدمة"]
}
\`\`\`

**إرشادات مهمة:**
- استخدم أفضل الممارسات في SQL
- تأكد من صحة أسماء الجداول والأعمدة الموجودة فقط
- استخدم JOIN المناسب عند الحاجة
- أضف WHERE clauses مناسبة
- **مهم جداً:** عند استخدام JOIN، حدد اسم الجدول لكل عمود لتجنب Ambiguous column name
- مثال صحيح: SELECT t1.ClientID, t1.ClientName FROM table1 t1 JOIN table2 t2 ON t1.ID = t2.ID
- مثال خاطئ: SELECT ClientID, ClientName FROM table1 JOIN table2 ON table1.ID = table2.ID
- استخدم aliases للجداول (t1, t2, etc.) لتوضيح العلاقات
- **أفضل:** استخدم جدول واحد إذا كان يحتوي على جميع البيانات المطلوبة
- **تجنب JOIN** إلا إذا كان ضرورياً جداً للحصول على البيانات
${databaseType === 'mssql' ? `
**خاص بـ SQL Server:**
- استخدم TOP بدلاً من LIMIT (مثال: SELECT TOP 10 * FROM table)
- استخدم [] حول أسماء الأعمدة إذا احتوت على مسافات
- لا تستخدم LIMIT أبداً - استخدم TOP فقط
` : `
**خاص بـ MySQL:**
- استخدم LIMIT للحد من النتائج (مثال: LIMIT 10)
- استخدم backticks \`\` حول أسماء الجداول إذا احتوت على كلمات محجوزة
`}
- أرجع JSON صحيح فقط
`;
  }

  /**
   * تحليل رد تحليل الجدول
   */
  private parseTableAnalysisResponse(content: string): any {
    try {
      // البحث عن JSON في النص
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0];
        return JSON.parse(jsonStr);
      }

      // إذا لم يتم العثور على JSON، محاولة تحليل النص كاملاً
      return JSON.parse(content);
    } catch (error) {
      console.error('خطأ في تحليل رد تحليل الجدول:', error);
      // إرجاع قيم افتراضية في حالة الخطأ
      return {
        description: `جدول ${content.substring(0, 100)}...`,
        columnDescriptions: {},
        analyticalValue: 'غير محدد',
        sqlExamples: [],
        intelligentAnalysis: 'تعذر التحليل',
        purpose: 'غير محدد',
        domain: 'عام',
        businessContext: 'غير محدد',
        keyFields: [],
        relatedTables: [],
        limitations: 'غير محدد'
      };
    }
  }

  /**
   * تحليل رد توليد SQL
   */
  private parseSQLResponse(content: string): any {
    try {
      // البحث عن JSON في النص
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0];
        return JSON.parse(jsonStr);
      }

      // إذا لم يتم العثور على JSON، محاولة تحليل النص كاملاً
      return JSON.parse(content);
    } catch (error) {
      console.error('خطأ في تحليل رد SQL:', error);
      // إرجاع قيم افتراضية في حالة الخطأ
      return {
        query: 'SELECT 1;',
        explanation: 'تعذر توليد الاستعلام',
        confidence: 0,
        relevantTables: []
      };
    }
  }

  /**
   * تهيئة الوكيل الذكي (للتوافق مع Gemini client)
   */
  async initializeIntelligentAgent(tablesInfo: Array<{
    name: string;
    description: string;
    columns: Array<{name: string, type: string}>;
    relationships: Array<{toTable: string, fromColumn: string, toColumn: string}>;
  }>): Promise<void> {
    // هذه الدالة للتوافق مع واجهة Gemini client
    // OpenRouter لا يحتاج تهيئة خاصة للوكيل الذكي
    console.log('تم تهيئة OpenRouter client مع', tablesInfo.length, 'جدول');
  }
}

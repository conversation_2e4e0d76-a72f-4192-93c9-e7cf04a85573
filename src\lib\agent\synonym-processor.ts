import synonymsData from './synonyms.json';

export interface ProcessedQuery {
  originalQuery: string;
  normalizedQuery: string;
  expandedQuery: string;
  detectedPatterns: DetectedPattern[];
  extractedNumbers: ExtractedNumber[];
  replacements: Replacement[];
}

export interface DetectedPattern {
  type: string;
  pattern: string;
  match: string;
  start: number;
  end: number;
  variables: { [key: string]: string };
}

export interface ExtractedNumber {
  original: string;
  numeric: number;
  start: number;
  end: number;
}

export interface Replacement {
  original: string;
  replacement: string;
  type: 'synonym' | 'number' | 'abbreviation';
  start: number;
  end: number;
}

/**
 * معالج المرادفات والأنماط - يوسع فهم الاستعلامات ويطبعها
 */
export class SynonymProcessor {
  private synonyms: { [category: string]: { [term: string]: string[] } };
  private patterns: { [category: string]: any[] };
  private arabicNumbers: { [arabic: string]: string };
  private abbreviations: { [abbr: string]: string };
  private businessContext: { [context: string]: any };

  constructor() {
    this.synonyms = synonymsData.synonyms;
    this.patterns = synonymsData.patterns;
    this.arabicNumbers = synonymsData.arabic_numbers;
    this.abbreviations = synonymsData.common_abbreviations;
    this.businessContext = synonymsData.business_context;
  }

  /**
   * معالجة الاستعلام وتوسيعه بالمرادفات والأنماط
   */
  processQuery(query: string): ProcessedQuery {
    const originalQuery = query;
    let normalizedQuery = this.normalizeQuery(query);
    
    // استخراج الأرقام
    const extractedNumbers = this.extractNumbers(normalizedQuery);
    
    // استبدال الأرقام العربية بالإنجليزية
    normalizedQuery = this.replaceArabicNumbers(normalizedQuery);
    
    // استبدال الاختصارات
    normalizedQuery = this.replaceAbbreviations(normalizedQuery);
    
    // كشف الأنماط
    const detectedPatterns = this.detectPatterns(normalizedQuery);
    
    // توسيع المرادفات
    const { expandedQuery, replacements } = this.expandSynonyms(normalizedQuery);
    
    return {
      originalQuery,
      normalizedQuery,
      expandedQuery,
      detectedPatterns,
      extractedNumbers,
      replacements
    };
  }

  /**
   * تطبيع الاستعلام الأساسي
   */
  private normalizeQuery(query: string): string {
    return query
      .trim()
      .replace(/\s+/g, ' ') // توحيد المسافات
      .replace(/[؟!]/g, '?') // توحيد علامات الاستفهام
      .replace(/[،]/g, ',') // توحيد الفواصل
      .toLowerCase();
  }

  /**
   * استخراج الأرقام من النص
   */
  private extractNumbers(query: string): ExtractedNumber[] {
    const numbers: ExtractedNumber[] = [];
    
    // البحث عن الأرقام الإنجليزية
    const englishNumberRegex = /\d+(\.\d+)?/g;
    let match;
    
    while ((match = englishNumberRegex.exec(query)) !== null) {
      numbers.push({
        original: match[0],
        numeric: parseFloat(match[0]),
        start: match.index,
        end: match.index + match[0].length
      });
    }
    
    // البحث عن الأرقام العربية المكتوبة
    for (const [arabic, numeric] of Object.entries(this.arabicNumbers)) {
      const regex = new RegExp(`\\b${arabic}\\b`, 'g');
      while ((match = regex.exec(query)) !== null) {
        numbers.push({
          original: arabic,
          numeric: parseInt(numeric),
          start: match.index,
          end: match.index + arabic.length
        });
      }
    }
    
    return numbers;
  }

  /**
   * استبدال الأرقام العربية بالإنجليزية
   */
  private replaceArabicNumbers(query: string): string {
    let result = query;
    
    for (const [arabic, numeric] of Object.entries(this.arabicNumbers)) {
      const regex = new RegExp(`\\b${arabic}\\b`, 'g');
      result = result.replace(regex, numeric);
    }
    
    return result;
  }

  /**
   * استبدال الاختصارات بالنص الكامل
   */
  private replaceAbbreviations(query: string): string {
    let result = query;
    
    for (const [abbr, full] of Object.entries(this.abbreviations)) {
      const regex = new RegExp(`\\b${abbr}\\b`, 'g');
      result = result.replace(regex, full);
    }
    
    return result;
  }

  /**
   * كشف الأنماط في النص
   */
  private detectPatterns(query: string): DetectedPattern[] {
    const detectedPatterns: DetectedPattern[] = [];
    
    for (const [category, patterns] of Object.entries(this.patterns)) {
      for (const patternObj of patterns) {
        const matches = this.findPatternMatches(query, patternObj, category);
        detectedPatterns.push(...matches);
      }
    }
    
    return detectedPatterns;
  }

  /**
   * البحث عن تطابقات نمط معين
   */
  private findPatternMatches(query: string, patternObj: any, category: string): DetectedPattern[] {
    const matches: DetectedPattern[] = [];
    const pattern = patternObj.pattern;
    
    // تحويل النمط إلى regex
    const regexPattern = pattern
      .replace(/\{([^}]+)\}/g, '([^\\s]+)') // استبدال المتغيرات
      .replace(/\s+/g, '\\s+'); // السماح بمسافات متعددة
    
    const regex = new RegExp(regexPattern, 'gi');
    let match;
    
    while ((match = regex.exec(query)) !== null) {
      // استخراج المتغيرات
      const variables: { [key: string]: string } = {};
      const variableNames = pattern.match(/\{([^}]+)\}/g);
      
      if (variableNames) {
        variableNames.forEach((varName, index) => {
          const cleanVarName = varName.replace(/[{}]/g, '');
          variables[cleanVarName] = match[index + 1] || '';
        });
      }
      
      matches.push({
        type: patternObj.type,
        pattern: pattern,
        match: match[0],
        start: match.index,
        end: match.index + match[0].length,
        variables
      });
    }
    
    return matches;
  }

  /**
   * توسيع المرادفات
   */
  private expandSynonyms(query: string): { expandedQuery: string; replacements: Replacement[] } {
    let expandedQuery = query;
    const replacements: Replacement[] = [];
    
    for (const [category, synonymGroup] of Object.entries(this.synonyms)) {
      for (const [mainTerm, synonyms] of Object.entries(synonymGroup)) {
        // استبدال المرادفات بالمصطلح الرئيسي
        for (const synonym of synonyms) {
          const regex = new RegExp(`\\b${synonym}\\b`, 'gi');
          const matches = [...query.matchAll(regex)];
          
          for (const match of matches) {
            if (match.index !== undefined) {
              replacements.push({
                original: synonym,
                replacement: mainTerm,
                type: 'synonym',
                start: match.index,
                end: match.index + synonym.length
              });
            }
          }
          
          expandedQuery = expandedQuery.replace(regex, mainTerm);
        }
      }
    }
    
    return { expandedQuery, replacements };
  }

  /**
   * الحصول على مرادفات مصطلح معين
   */
  getSynonyms(term: string): string[] {
    const allSynonyms: string[] = [];
    
    for (const synonymGroup of Object.values(this.synonyms)) {
      for (const [mainTerm, synonyms] of Object.entries(synonymGroup)) {
        if (mainTerm.toLowerCase() === term.toLowerCase()) {
          allSynonyms.push(...synonyms);
        } else if (synonyms.some(syn => syn.toLowerCase() === term.toLowerCase())) {
          allSynonyms.push(mainTerm, ...synonyms.filter(syn => syn.toLowerCase() !== term.toLowerCase()));
        }
      }
    }
    
    return [...new Set(allSynonyms)]; // إزالة التكرارات
  }

  /**
   * إضافة مرادف جديد
   */
  addSynonym(category: string, mainTerm: string, synonym: string): void {
    if (!this.synonyms[category]) {
      this.synonyms[category] = {};
    }
    
    if (!this.synonyms[category][mainTerm]) {
      this.synonyms[category][mainTerm] = [];
    }
    
    if (!this.synonyms[category][mainTerm].includes(synonym)) {
      this.synonyms[category][mainTerm].push(synonym);
    }
  }

  /**
   * الحصول على جميع الأنماط المتاحة
   */
  getAvailablePatterns(): { [category: string]: any[] } {
    return this.patterns;
  }

  /**
   * تحديد السياق التجاري للاستعلام
   */
  detectBusinessContext(query: string): string[] {
    const contexts: string[] = [];
    
    for (const [context, contextData] of Object.entries(this.businessContext)) {
      const terms = contextData.terms || [];
      const synonyms = contextData.synonyms || [];
      
      const allTerms = [...terms, ...synonyms];
      
      for (const term of allTerms) {
        if (query.toLowerCase().includes(term.toLowerCase())) {
          contexts.push(context);
          break;
        }
      }
    }
    
    return [...new Set(contexts)]; // إزالة التكرارات
  }
}

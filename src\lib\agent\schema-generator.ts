import { SchemaManager } from '../database/schema-manager';

export interface TableColumn {
  name: string;
  type: string;
  description: string;
  isImportant: boolean;
  category: 'id' | 'name' | 'amount' | 'date' | 'quantity' | 'status' | 'reference' | 'other';
}

export interface TableSchema {
  name: string;
  description: string;
  columns: TableColumn[];
  primaryKey?: string;
  relationships?: TableRelationship[];
}

export interface TableRelationship {
  type: 'one-to-many' | 'many-to-one' | 'many-to-many';
  targetTable: string;
  foreignKey: string;
  description: string;
}

export interface DatabaseSchema {
  tables: TableSchema[];
  version: string;
  generatedAt: string;
  metadata: {
    totalTables: number;
    totalColumns: number;
    importantColumns: number;
  };
}

/**
 * مولد هيكل الجداول الذكي - يحلل قاعدة البيانات وينشئ Schema محسن
 */
export class SchemaGenerator {
  private schemaManager: SchemaManager;
  
  // الكلمات المفتاحية للأعمدة المهمة
private importantKeywords = {
  id: [
    'id', 'key', 'code', 'recordid', 'invoiceid', 'documentid',
    'detailsid', 'itemid', 'unitid', 'clientid', 'storeid',
    'userid', 'branchid', 'costcenterid', 'barcodeid'
  ],
  name: [
    'name', 'title', 'label', 'description',
    'itemname', 'clientname', 'suppliername', 'username',
    'distributorname', 'currencyname', 'categoryname',
    'unitname', 'branchname', 'accountname', 'storename',
    'method', 'documentname', 'costcentername'
  ],
  amount: [
    'amount', 'totalamount', 'mcamount', 'value',
    'price', 'unitprice', 'mainunitprice', 'exchangeprice',
    'total', 'sum', 'mcamountcurrencymain', 'totalamountbycurrencyinvetory'
  ],
  quantity: [
    'quantity', 'qty', 'bonus', 'mainunitquantity',
    'packagequantity', 'count', 'num'
  ],
  date: [
    'date', 'thedate', 'enterdate', 'created', 'updated',
    'expirydate', 'entertime', 'modified'
  ],
  status: [
    'status', 'isactive', 'isexpiry', 'state', 'flag', 'enabled'
  ],
  reference: [
    'ref', 'reference', 'relation', 'link', 'parentid', 'nextparentid',
    'categoryid', 'fatherid', 'recordnumber', 'documentname',
    'accountnumber', 'costcenternumber', 'itemnumber'
  ]
};


  // أنماط الأعمدة غير المهمة
  private unimportantPatterns = [
    /^created_by$/i,
    /^updated_by$/i,
    /^deleted_at$/i,
    /^version$/i,
    /^timestamp$/i,
    /^guid$/i,
    /^uuid$/i,
    /^hash$/i,
    /^checksum$/i
  ];

  constructor() {
    this.schemaManager = SchemaManager.getInstance();
  }

  /**
   * توليد Schema كامل لقاعدة البيانات
   */
  async generateDatabaseSchema(): Promise<DatabaseSchema> {
    try {
      console.log('🔍 بدء تحليل قاعدة البيانات...');
      
      // الحصول على قائمة الجداول
      const tableNames = await this.getTableNames();
      console.log(`📊 تم العثور على ${tableNames.length} جدول`);

      const tables: TableSchema[] = [];
      let totalColumns = 0;
      let importantColumns = 0;

      // تحليل كل جدول
      for (const tableName of tableNames) {
        console.log(`🔍 تحليل الجدول: ${tableName}`);
        
        const tableSchema = await this.generateTableSchema(tableName);
        if (tableSchema) {
          tables.push(tableSchema);
          totalColumns += tableSchema.columns.length;
          importantColumns += tableSchema.columns.filter(col => col.isImportant).length;
        }
      }

      const schema: DatabaseSchema = {
        tables,
        version: '1.0.0',
        generatedAt: new Date().toISOString(),
        metadata: {
          totalTables: tables.length,
          totalColumns,
          importantColumns
        }
      };

      console.log(`✅ تم توليد Schema بنجاح: ${tables.length} جدول، ${importantColumns}/${totalColumns} عمود مهم`);
      return schema;

    } catch (error) {
      console.error('❌ خطأ في توليد Schema:', error);
      throw error;
    }
  }

  /**
   * توليد Schema لجدول واحد
   */
  async generateTableSchema(tableName: string): Promise<TableSchema | null> {
    try {
      // الحصول على معلومات الأعمدة
      const columns = await this.getTableColumns(tableName);
      if (!columns || columns.length === 0) {
        console.warn(`⚠️ لا توجد أعمدة للجدول: ${tableName}`);
        return null;
      }

      // تحليل الأعمدة وتصنيفها
      const analyzedColumns = columns.map(col => this.analyzeColumn(col));

      // توليد وصف الجدول
      const description = this.generateTableDescription(tableName, analyzedColumns);

      // البحث عن المفتاح الأساسي
      const primaryKey = this.findPrimaryKey(analyzedColumns);

      const tableSchema: TableSchema = {
        name: tableName,
        description,
        columns: analyzedColumns,
        primaryKey
      };

      return tableSchema;

    } catch (error) {
      console.error(`❌ خطأ في تحليل الجدول ${tableName}:`, error);
      return null;
    }
  }

  /**
   * تحليل عمود وتحديد أهميته وفئته
   */
  private analyzeColumn(column: any): TableColumn {
    const columnName = column.COLUMN_NAME || column.name || '';
    const dataType = column.DATA_TYPE || column.type || '';
    const comment = column.COLUMN_COMMENT || column.comment || '';

    // تحديد فئة العمود
    const category = this.categorizeColumn(columnName, dataType);
    
    // تحديد أهمية العمود
    const isImportant = this.isColumnImportant(columnName, dataType, category);

    // توليد وصف العمود
    const description = this.generateColumnDescription(columnName, dataType, category, comment);

    return {
      name: columnName,
      type: dataType,
      description,
      isImportant,
      category
    };
  }

  /**
   * تصنيف العمود حسب نوعه
   */
  public categorizeColumn(columnName: string, dataType: string): TableColumn['category'] {
    const lowerName = columnName.toLowerCase();

    // فحص المعرفات
    if (this.importantKeywords.id.some(keyword => lowerName.includes(keyword))) {
      return 'id';
    }

    // فحص الأسماء
    if (this.importantKeywords.name.some(keyword => lowerName.includes(keyword))) {
      return 'name';
    }

    // فحص المبالغ
    if (this.importantKeywords.amount.some(keyword => lowerName.includes(keyword))) {
      return 'amount';
    }

    // فحص التواريخ
    if (this.importantKeywords.date.some(keyword => lowerName.includes(keyword)) || 
        dataType.toLowerCase().includes('date') || dataType.toLowerCase().includes('time')) {
      return 'date';
    }

    // فحص الكميات
    if (this.importantKeywords.quantity.some(keyword => lowerName.includes(keyword))) {
      return 'quantity';
    }

    // فحص الحالات
    if (this.importantKeywords.status.some(keyword => lowerName.includes(keyword))) {
      return 'status';
    }

    // فحص المراجع
    if (this.importantKeywords.reference.some(keyword => lowerName.includes(keyword))) {
      return 'reference';
    }

    return 'other';
  }

  /**
   * تحديد ما إذا كان العمود مهماً
   */
  public isColumnImportant(columnName: string, dataType: string, category: TableColumn['category']): boolean {
    const lowerName = columnName.toLowerCase();

    // الأعمدة المهمة دائماً
    if (['id', 'name', 'amount', 'quantity'].includes(category)) {
      return true;
    }

    // التواريخ المهمة
    if (category === 'date' && !lowerName.includes('created_at') && !lowerName.includes('updated_at')) {
      return true;
    }

    // الحالات المهمة
    if (category === 'status') {
      return true;
    }

    // فحص الأنماط غير المهمة
    if (this.unimportantPatterns.some(pattern => pattern.test(columnName))) {
      return false;
    }

    // أعمدة أخرى قد تكون مهمة
    const businessKeywords = ['client', 'customer', 'branch', 'store', 'product', 'item', 'document', 'invoice'];
    if (businessKeywords.some(keyword => lowerName.includes(keyword))) {
      return true;
    }

    return false;
  }

  /**
   * توليد وصف للعمود
   */
  public generateColumnDescription(columnName: string, dataType: string, category: TableColumn['category'], comment?: string): string {
    if (comment && comment.trim()) {
      return comment.trim();
    }

    const lowerName = columnName.toLowerCase();

    // أوصاف حسب الفئة
    switch (category) {
      case 'id':
        if (lowerName.includes('client') || lowerName.includes('customer')) {
          return 'معرف العميل';
        }
        if (lowerName.includes('product') || lowerName.includes('item')) {
          return 'معرف المنتج';
        }
        if (lowerName.includes('branch')) {
          return 'معرف الفرع';
        }
        if (lowerName.includes('invoice') || lowerName.includes('document')) {
          return 'معرف الفاتورة';
        }
        return 'معرف فريد للسجل';

      case 'name':
        if (lowerName.includes('client') || lowerName.includes('customer')) {
          return 'اسم العميل';
        }
        if (lowerName.includes('product') || lowerName.includes('item')) {
          return 'اسم المنتج';
        }
        if (lowerName.includes('branch')) {
          return 'اسم الفرع';
        }
        if (lowerName.includes('document')) {
          return 'نوع المستند';
        }
        return 'اسم أو وصف';

      case 'amount':
        if (lowerName.includes('price')) {
          return 'السعر';
        }
        if (lowerName.includes('total')) {
          return 'المبلغ الإجمالي';
        }
        if (lowerName.includes('cost')) {
          return 'التكلفة';
        }
        return 'مبلغ مالي';

      case 'quantity':
        return 'الكمية';

      case 'date':
        if (lowerName.includes('created')) {
          return 'تاريخ الإنشاء';
        }
        if (lowerName.includes('updated')) {
          return 'تاريخ التحديث';
        }
        return 'التاريخ';

      case 'status':
        return 'حالة السجل';

      default:
        return `عمود من نوع ${dataType}`;
    }
  }

  /**
   * توليد وصف للجدول
   */
  public generateTableDescription(tableName: string, columns: TableColumn[]): string {
    const lowerName = tableName.toLowerCase();

    // أوصاف خاصة للجداول المعروفة
    if (lowerName.includes('items') || lowerName.includes('item')) {
      return 'جدول يحتوي على تفاصيل العناصر والمنتجات في الفواتير';
    }
    
    if (lowerName.includes('invoice') || lowerName.includes('main')) {
      return 'جدول رئيسي يحتوي على معلومات الفواتير';
    }

    if (lowerName.includes('client') || lowerName.includes('customer')) {
      return 'جدول معلومات العملاء';
    }

    if (lowerName.includes('product')) {
      return 'جدول معلومات المنتجات';
    }

    if (lowerName.includes('branch')) {
      return 'جدول معلومات الفروع';
    }

    // وصف عام بناءً على الأعمدة
    const hasAmount = columns.some(col => col.category === 'amount');
    const hasQuantity = columns.some(col => col.category === 'quantity');
    const hasDate = columns.some(col => col.category === 'date');
    const hasClient = columns.some(col => col.name.toLowerCase().includes('client'));

    if (hasAmount && hasQuantity && hasDate) {
      return 'جدول يحتوي على بيانات المعاملات المالية والكميات';
    }

    if (hasClient && hasAmount) {
      return 'جدول يحتوي على معاملات العملاء والمبالغ';
    }

    return `جدول ${tableName} يحتوي على ${columns.length} عمود`;
  }

  /**
   * البحث عن المفتاح الأساسي
   */
  private findPrimaryKey(columns: TableColumn[]): string | undefined {
    // البحث عن عمود ID
    const idColumn = columns.find(col => 
      col.name.toLowerCase() === 'id' || 
      col.name.toLowerCase().endsWith('id') ||
      col.category === 'id'
    );

    return idColumn?.name;
  }

  /**
   * الحصول على قائمة أسماء الجداول
   */
  private async getTableNames(): Promise<string[]> {
    try {
      const query = `
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
      `;
      
      const result = await this.schemaManager.executeQuery(query);
      return result.data?.map((row: any) => row.TABLE_NAME) || [];
    } catch (error) {
      console.error('خطأ في الحصول على أسماء الجداول:', error);
      return [];
    }
  }

  /**
   * الحصول على معلومات أعمدة الجدول
   */
  private async getTableColumns(tableName: string): Promise<any[]> {
    try {
      const query = `
        SELECT 
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          CHARACTER_MAXIMUM_LENGTH,
          NUMERIC_PRECISION,
          NUMERIC_SCALE,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = '${tableName}'
        ORDER BY ORDINAL_POSITION
      `;
      
      const result = await this.schemaManager.executeQuery(query);
      return result.data || [];
    } catch (error) {
      console.error(`خطأ في الحصول على أعمدة الجدول ${tableName}:`, error);
      return [];
    }
  }

  /**
   * حفظ Schema في ملف
   */
  async saveSchemaToFile(schema: DatabaseSchema, filename: string = 'database-schema.json'): Promise<void> {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const dataDir = path.join(process.cwd(), 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }
      
      const filePath = path.join(dataDir, filename);
      fs.writeFileSync(filePath, JSON.stringify(schema, null, 2));
      
      console.log(`✅ تم حفظ Schema في: ${filePath}`);
    } catch (error) {
      console.error('❌ خطأ في حفظ Schema:', error);
      throw error;
    }
  }

  /**
   * تحميل Schema من ملف
   */
  async loadSchemaFromFile(filename: string = 'database-schema.json'): Promise<DatabaseSchema | null> {
    try {
      const fs = require('fs');
      const path = require('path');

      const filePath = path.join(process.cwd(), 'data', filename);

      if (!fs.existsSync(filePath)) {
        console.warn(`⚠️ ملف Schema غير موجود: ${filePath}`);
        return null;
      }

      const data = fs.readFileSync(filePath, 'utf8');
      const rawSchema = JSON.parse(data);

      // تحويل البيانات المحملة إلى DatabaseSchema مع إضافة الخصائص المفقودة
      const schema: DatabaseSchema = {
        tables: rawSchema.tables?.map((table: any) => ({
          name: table.name,
          description: table.description || this.generateTableDescription(table.name, []),
          columns: table.columns?.map((col: any) => {
            // إذا كانت الخصائص موجودة، استخدمها، وإلا احسبها
            const category = col.category || this.categorizeColumn(col.name, col.type);
            const isImportant = col.isImportant !== undefined ? col.isImportant : this.isColumnImportant(col.name, col.type, category);
            const description = col.description || this.generateColumnDescription(col.name, col.type, category);

            return {
              name: col.name,
              type: col.type,
              description,
              isImportant,
              category
            };
          }) || [],
          primaryKey: table.primaryKey
        })) || [],
        version: rawSchema.version || '1.0',
        generatedAt: rawSchema.generatedAt || new Date().toISOString(),
        metadata: rawSchema.metadata || {
          totalTables: rawSchema.tables?.length || 0,
          totalColumns: 0,
          importantColumns: 0
        }
      };

      console.log(`✅ تم تحميل Schema من: ${filePath}`);
      return schema;
    } catch (error) {
      console.error('❌ خطأ في تحميل Schema:', error);
      return null;
    }
  }

  /**
   * الحصول على الأعمدة المهمة فقط من جدول
   */
  getImportantColumns(tableName: string, schema: DatabaseSchema): TableColumn[] {
    const table = schema.tables.find(t => t.name === tableName);
    if (!table) return [];
    
    return table.columns.filter(col => col.isImportant);
  }

  /**
   * توليد نص وصفي للجدول للاستخدام في البرومبت
   */
  generateTableInfoText(tableName: string, schema: DatabaseSchema): string {
    const table = schema.tables.find(t => t.name === tableName);
    if (!table) return '';

    const importantColumns = table.columns.filter(col => col.isImportant);
    
    let text = `جدول ${tableName}:\n`;
    text += `${table.description}\n\n`;
    text += 'الأعمدة المهمة:\n';
    
    importantColumns.forEach(col => {
      text += `- ${col.name} (${col.type}): ${col.description}\n`;
    });

    return text;
  }
}

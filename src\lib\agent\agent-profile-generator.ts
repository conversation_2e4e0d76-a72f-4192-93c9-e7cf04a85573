import { DatabaseSchema, TableSchema, TableColumn } from './schema-generator';
import { OpenRouterClient } from '../ai/openrouter-client';

export interface UseCase {
  name: string;
  description: string;
  example_sql: string;
  intent_category: string;
  parameters: string[];
}

export interface AgentProfile {
  table: string;
  description: string;
  fields: { [fieldName: string]: string };
  use_cases: UseCase[];
  business_context: {
    domain: string;
    primary_purpose: string;
    key_metrics: string[];
    common_filters: string[];
  };
  sql_patterns: {
    aggregations: string[];
    joins: string[];
    filters: string[];
  };
}

/**
 * مولد ملف التوصيف الذكي - ينشئ وصف تفصيلي للجداول مع أمثلة SQL
 */
export class AgentProfileGenerator {
  private openRouterClient: OpenRouterClient;

  // قوالب حالات الاستخدام الشائعة
  private commonUseCases = {
    sales: [
      {
        name: 'إجمالي المبيعات لفترة معينة',
        intent: 'sales_total',
        template: 'SELECT SUM({amount_column}) as total_sales FROM {table} WHERE {document_filter} AND {date_column} BETWEEN @start_date AND @end_date'
      },
      {
        name: 'أكثر المنتجات مبيعاً',
        intent: 'top_products',
        template: 'SELECT TOP {limit} {product_column}, SUM({quantity_column}) as total_quantity FROM {table} WHERE {document_filter} GROUP BY {product_column} ORDER BY total_quantity DESC'
      },
      {
        name: 'أفضل العملاء',
        intent: 'customer_analysis',
        template: 'SELECT TOP {limit} {client_column}, SUM({amount_column}) as total_amount FROM {table} WHERE {document_filter} GROUP BY {client_column} ORDER BY total_amount DESC'
      }
    ],
    inventory: [
      {
        name: 'حالة المخزون الحالية',
        intent: 'inventory_status',
        template: 'SELECT {product_column}, SUM(CASE WHEN {document_filter_in} THEN {quantity_column} ELSE -{quantity_column} END) as current_stock FROM {table} GROUP BY {product_column}'
      }
    ],
    financial: [
      {
        name: 'تحليل الأرباح والخسائر',
        intent: 'financial_reports',
        template: 'SELECT SUM(CASE WHEN {sales_filter} THEN {amount_column} ELSE 0 END) as revenue, SUM(CASE WHEN {purchase_filter} THEN {amount_column} ELSE 0 END) as expenses FROM {table} WHERE {date_column} BETWEEN @start_date AND @end_date'
      }
    ]
  };

  constructor() {
    this.openRouterClient = OpenRouterClient.getInstance();
  }

  /**
   * توليد Agent Profile لجدول معين
   */
  async generateAgentProfile(tableName: string, schema: DatabaseSchema): Promise<AgentProfile | null> {
    try {
      console.log(`🤖 توليد Agent Profile للجدول: ${tableName}`);

      const table = schema.tables.find(t => t.name === tableName);
      if (!table) {
        console.error(`❌ الجدول ${tableName} غير موجود في Schema`);
        return null;
      }

      // تحليل الجدول وتحديد السياق التجاري
      const businessContext = this.analyzeBusinessContext(table);
      
      // توليد أوصاف الحقول
      const fields = this.generateFieldDescriptions(table);
      
      // توليد حالات الاستخدام
      const useCases = await this.generateUseCases(table, businessContext);
      
      // توليد أنماط SQL
      const sqlPatterns = this.generateSQLPatterns(table);

      const profile: AgentProfile = {
        table: tableName,
        description: this.enhanceTableDescription(table, businessContext),
        fields,
        use_cases: useCases,
        business_context: businessContext,
        sql_patterns: sqlPatterns
      };

      console.log(`✅ تم توليد Agent Profile بنجاح: ${useCases.length} حالة استخدام`);
      return profile;

    } catch (error) {
      console.error(`❌ خطأ في توليد Agent Profile للجدول ${tableName}:`, error);
      return null;
    }
  }

  /**
   * تحليل السياق التجاري للجدول
   */
  private analyzeBusinessContext(table: TableSchema): AgentProfile['business_context'] {
    const tableName = table.name.toLowerCase();
    const columns = table.columns;

    // تحديد المجال التجاري
    let domain = 'general';
    let primaryPurpose = 'تخزين البيانات العامة';

    if (tableName.includes('item') || tableName.includes('invoice') || tableName.includes('sales')) {
      domain = 'sales_and_inventory';
      primaryPurpose = 'تتبع المبيعات والمخزون';
    } else if (tableName.includes('client') || tableName.includes('customer')) {
      domain = 'customer_management';
      primaryPurpose = 'إدارة معلومات العملاء';
    } else if (tableName.includes('product')) {
      domain = 'product_management';
      primaryPurpose = 'إدارة معلومات المنتجات';
    } else if (tableName.includes('financial') || tableName.includes('account')) {
      domain = 'financial';
      primaryPurpose = 'إدارة البيانات المالية';
    }

    // تحديد المقاييس الرئيسية
    const keyMetrics: string[] = [];
    if (columns.some(col => col.category === 'amount')) {
      keyMetrics.push('المبالغ المالية', 'الإجماليات');
    }
    if (columns.some(col => col.category === 'quantity')) {
      keyMetrics.push('الكميات', 'المخزون');
    }
    if (columns.some(col => col.name.toLowerCase().includes('client'))) {
      keyMetrics.push('عدد العملاء', 'تحليل العملاء');
    }

    // تحديد الفلاتر الشائعة
    const commonFilters: string[] = [];
    if (columns.some(col => col.category === 'date')) {
      commonFilters.push('فلتر التاريخ', 'فترة زمنية');
    }
    if (columns.some(col => col.name.toLowerCase().includes('branch'))) {
      commonFilters.push('فلتر الفرع');
    }
    if (columns.some(col => col.name.toLowerCase().includes('document'))) {
      commonFilters.push('نوع المستند');
    }

    return {
      domain,
      primary_purpose: primaryPurpose,
      key_metrics: keyMetrics,
      common_filters: commonFilters
    };
  }

  /**
   * توليد أوصاف الحقول
   */
  private generateFieldDescriptions(table: TableSchema): { [fieldName: string]: string } {
    const fields: { [fieldName: string]: string } = {};
    
    // التركيز على الأعمدة المهمة فقط
    const importantColumns = table.columns.filter(col => col.isImportant);
    
    importantColumns.forEach(column => {
      fields[column.name] = this.enhanceColumnDescription(column, table);
    });

    return fields;
  }

  /**
   * تحسين وصف العمود
   */
  private enhanceColumnDescription(column: TableColumn, table: TableSchema): string {
    let description = column.description;
    
    // إضافة سياق تجاري
    const columnName = column.name.toLowerCase();
    
    if (column.category === 'amount' && columnName.includes('total')) {
      description += ' - يستخدم في حساب الإجماليات والتقارير المالية';
    } else if (column.category === 'quantity') {
      description += ' - مهم لتتبع المخزون وحساب الكميات المباعة';
    } else if (column.category === 'date') {
      description += ' - يستخدم في التقارير الزمنية وتحليل الاتجاهات';
    } else if (columnName.includes('client') || columnName.includes('customer')) {
      description += ' - أساسي لتحليل العملاء وتقارير المبيعات';
    } else if (columnName.includes('branch')) {
      description += ' - يستخدم في مقارنة أداء الفروع';
    } else if (columnName.includes('document')) {
      description += ' - يحدد نوع العملية (مبيعات، مشتريات، مرتجعات)';
    }

    return description;
  }

  /**
   * توليد حالات الاستخدام
   */
  private async generateUseCases(table: TableSchema, businessContext: AgentProfile['business_context']): Promise<UseCase[]> {
    const useCases: UseCase[] = [];
    
    // تحديد الأعمدة المهمة
    const amountColumn = table.columns.find(col => col.category === 'amount')?.name;
    const quantityColumn = table.columns.find(col => col.category === 'quantity')?.name;
    const dateColumn = table.columns.find(col => col.category === 'date')?.name;
    const productColumn = table.columns.find(col => col.name.toLowerCase().includes('item') || col.name.toLowerCase().includes('product'))?.name;
    const clientColumn = table.columns.find(col => col.name.toLowerCase().includes('client') || col.name.toLowerCase().includes('customer'))?.name;
    const branchColumn = table.columns.find(col => col.name.toLowerCase().includes('branch'))?.name;
    const documentColumn = table.columns.find(col => col.name.toLowerCase().includes('document'))?.name;

    // توليد حالات استخدام للمبيعات
    if (amountColumn && dateColumn && documentColumn) {
      useCases.push({
        name: 'إجمالي المبيعات لفترة معينة',
        description: 'حساب مجموع المبيعات خلال فترة زمنية محددة',
        example_sql: `SELECT SUM(${amountColumn}) as total_sales FROM ${table.name} WHERE ${documentColumn} LIKE '%مبيعات%' AND ${dateColumn} BETWEEN @start_date AND @end_date`,
        intent_category: 'sales_total',
        parameters: ['start_date', 'end_date']
      });
    }

    // توليد حالات استخدام للمنتجات
    if (productColumn && quantityColumn && documentColumn) {
      useCases.push({
        name: 'أكثر المنتجات مبيعاً',
        description: 'عرض المنتجات الأكثر مبيعاً حسب الكمية',
        example_sql: `SELECT TOP 10 ${productColumn}, SUM(${quantityColumn}) as total_quantity FROM ${table.name} WHERE ${documentColumn} LIKE '%مبيعات%' GROUP BY ${productColumn} ORDER BY total_quantity DESC`,
        intent_category: 'top_products',
        parameters: ['limit']
      });
    }

    // توليد حالات استخدام للعملاء
    if (clientColumn && amountColumn && documentColumn) {
      useCases.push({
        name: 'أفضل العملاء',
        description: 'تحديد العملاء الذين حققوا أعلى مبيعات',
        example_sql: `SELECT TOP 10 ${clientColumn}, SUM(${amountColumn}) as total_amount FROM ${table.name} WHERE ${documentColumn} LIKE '%مبيعات%' GROUP BY ${clientColumn} ORDER BY total_amount DESC`,
        intent_category: 'customer_analysis',
        parameters: ['limit']
      });
    }

    // توليد حالات استخدام للفروع
    if (branchColumn && amountColumn && documentColumn) {
      useCases.push({
        name: 'أداء الفروع',
        description: 'مقارنة أداء الفروع المختلفة من حيث المبيعات',
        example_sql: `SELECT ${branchColumn}, SUM(${amountColumn}) as branch_sales FROM ${table.name} WHERE ${documentColumn} LIKE '%مبيعات%' GROUP BY ${branchColumn} ORDER BY branch_sales DESC`,
        intent_category: 'branch_performance',
        parameters: []
      });
    }

    // توليد حالات استخدام للمخزون
    if (productColumn && quantityColumn) {
      useCases.push({
        name: 'حالة المخزون',
        description: 'عرض الكميات المتاحة للمنتجات',
        example_sql: `SELECT ${productColumn}, SUM(CASE WHEN ${documentColumn} LIKE '%مبيعات%' THEN -${quantityColumn} WHEN ${documentColumn} LIKE '%مشتريات%' THEN ${quantityColumn} ELSE 0 END) as current_stock FROM ${table.name} GROUP BY ${productColumn}`,
        intent_category: 'inventory_status',
        parameters: []
      });
    }

    return useCases;
  }

  /**
   * توليد أنماط SQL
   */
  private generateSQLPatterns(table: TableSchema): AgentProfile['sql_patterns'] {
    const patterns: AgentProfile['sql_patterns'] = {
      aggregations: [],
      joins: [],
      filters: []
    };

    // أنماط التجميع
    if (table.columns.some(col => col.category === 'amount')) {
      patterns.aggregations.push('SUM(Amount) - لحساب إجمالي المبالغ');
      patterns.aggregations.push('AVG(Amount) - لحساب متوسط المبالغ');
    }
    
    if (table.columns.some(col => col.category === 'quantity')) {
      patterns.aggregations.push('SUM(Quantity) - لحساب إجمالي الكميات');
      patterns.aggregations.push('COUNT(*) - لعد السجلات');
    }

    // أنماط الفلترة
    if (table.columns.some(col => col.category === 'date')) {
      patterns.filters.push('WHERE TheDate BETWEEN @start_date AND @end_date - فلتر التاريخ');
      patterns.filters.push('WHERE YEAR(TheDate) = @year - فلتر السنة');
      patterns.filters.push('WHERE MONTH(TheDate) = @month - فلتر الشهر');
    }

    const documentColumn = table.columns.find(col => col.name.toLowerCase().includes('document'));
    if (documentColumn) {
      patterns.filters.push(`WHERE ${documentColumn.name} LIKE '%مبيعات%' - فلتر المبيعات`);
      patterns.filters.push(`WHERE ${documentColumn.name} LIKE '%مشتريات%' - فلتر المشتريات`);
    }

    const branchColumn = table.columns.find(col => col.name.toLowerCase().includes('branch'));
    if (branchColumn) {
      patterns.filters.push(`WHERE ${branchColumn.name} = @branch_name - فلتر الفرع`);
    }

    return patterns;
  }

  /**
   * تحسين وصف الجدول
   */
  private enhanceTableDescription(table: TableSchema, businessContext: AgentProfile['business_context']): string {
    let description = table.description;
    
    // إضافة سياق تجاري
    description += ` يخدم مجال ${businessContext.domain} ويهدف إلى ${businessContext.primary_purpose}.`;
    
    // إضافة معلومات عن الأعمدة المهمة
    const importantColumns = table.columns.filter(col => col.isImportant);
    description += ` يحتوي على ${importantColumns.length} عمود مهم من أصل ${table.columns.length} عمود.`;

    return description;
  }

  /**
   * حفظ Agent Profile في ملف
   */
  async saveProfileToFile(profile: AgentProfile, filename?: string): Promise<void> {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const dataDir = path.join(process.cwd(), 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }
      
      const fileName = filename || `agent-profile-${profile.table}.json`;
      const filePath = path.join(dataDir, fileName);
      
      fs.writeFileSync(filePath, JSON.stringify(profile, null, 2));
      
      console.log(`✅ تم حفظ Agent Profile في: ${filePath}`);
    } catch (error) {
      console.error('❌ خطأ في حفظ Agent Profile:', error);
      throw error;
    }
  }

  /**
   * تحميل Agent Profile من ملف
   */
  async loadProfileFromFile(filename: string): Promise<AgentProfile | null> {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const filePath = path.join(process.cwd(), 'data', filename);
      
      if (!fs.existsSync(filePath)) {
        console.warn(`⚠️ ملف Agent Profile غير موجود: ${filePath}`);
        return null;
      }
      
      const data = fs.readFileSync(filePath, 'utf8');
      const profile = JSON.parse(data) as AgentProfile;
      
      console.log(`✅ تم تحميل Agent Profile من: ${filePath}`);
      return profile;
    } catch (error) {
      console.error('❌ خطأ في تحميل Agent Profile:', error);
      return null;
    }
  }

  /**
   * توليد نص معلومات الجدول للاستخدام في البرومبت
   */
  generateTableInfoForPrompt(profile: AgentProfile): string {
    let text = `جدول ${profile.table}:\n`;
    text += `${profile.description}\n\n`;
    
    text += 'الحقول المهمة:\n';
    Object.entries(profile.fields).forEach(([field, description]) => {
      text += `- ${field}: ${description}\n`;
    });
    
    text += '\nحالات الاستخدام الشائعة:\n';
    profile.use_cases.forEach((useCase, index) => {
      text += `${index + 1}. ${useCase.name}: ${useCase.description}\n`;
    });
    
    return text;
  }
}

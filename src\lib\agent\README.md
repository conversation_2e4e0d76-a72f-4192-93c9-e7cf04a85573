# الوكيل الذكي الثلاثي الطبقات

نظام ذكاء صناعي متقدم لتحليل قواعد البيانات باستخدام OpenRouter مع بنية ثلاثية الطبقات احترافية.

## 🏗️ البنية المعمارية

```
User → Agent → (OpenRouter Model + Tools) → SQL → DB → Result → Agent → User
```

### الطبقات الثلاث الرئيسية:

1. **طبقة تصنيف النوايا (Intent Classification)**
   - تحليل استعلامات المستخدم
   - تحديد النية والكيانات
   - دعم 10+ أنواع استعلامات مختلفة

2. **طبقة معالجة المرادفات والأنماط (Synonyms & Patterns)**
   - توسيع فهم الاستعلامات
   - معالجة المرادفات العربية
   - كشف الأنماط الزمنية والرقمية

3. **طبقة قوالب البرومبت (Prompt Templates)**
   - قوالب SQL محسنة وآمنة
   - توليد برومبتات دقيقة
   - تحسين الأداء التلقائي

### الطبقات المساعدة:

- **نظام القواعد والأمان (Rules Engine)**
- **أدوات قاعدة البيانات (Database Tools)**
- **أدوات التحليل الذكي (Analysis Tools)**

## 🚀 الاستخدام السريع

```typescript
import { createIntelligentAgent, createUserContext } from '@/lib/agent';

// إنشاء الوكيل
const agent = createIntelligentAgent({
  enableCaching: true,
  debugMode: true
});

// إنشاء سياق المستخدم
const userContext = createUserContext('user_001', 'manager', 'الرياض');

// معالجة الاستعلام
const response = await agent.processQuery(
  'إجمالي المبيعات اليوم',
  userContext,
  tableInfo
);

console.log(response);
```

## 📊 النوايا المدعومة

| النية | الوصف | مثال |
|-------|--------|------|
| `sales_total` | إجمالي المبيعات | "إجمالي المبيعات اليوم" |
| `top_products` | أكثر المنتجات مبيعاً | "أكثر 5 منتجات مبيعاً" |
| `customer_analysis` | تحليل العملاء | "أفضل العملاء" |
| `inventory_status` | حالة المخزون | "حالة مخزون الهواتف" |
| `purchase_analysis` | تحليل المشتريات | "مشتريات من مورد معين" |
| `financial_reports` | التقارير المالية | "أرباح الشهر الماضي" |
| `branch_performance` | أداء الفروع | "مقارنة أداء الفروع" |
| `time_analysis` | التحليل الزمني | "اتجاه المبيعات الشهرية" |
| `product_movement` | حركة الأصناف | "حركة صنف معين" |
| `returns_analysis` | تحليل المرتجعات | "المنتجات المرتجعة" |

## 🔒 نظام الأمان

### أدوار المستخدمين:
- **Admin**: صلاحيات كاملة
- **Manager**: قراءة وكتابة
- **Employee**: قراءة فقط
- **Viewer**: قراءة محدودة

### الحماية من SQL Injection:
- فحص الأنماط الخبيثة
- تصفية العمليات المحظورة
- تحديد طول الاستعلامات
- تطبيق قواعد الأمان

## 📈 مميزات النظام

### ✨ الذكاء الاصطناعي:
- تصنيف ذكي للنوايا
- معالجة اللغة العربية
- فهم السياق والمرادفات
- توليد SQL محسن

### 🛡️ الأمان والحماية:
- نظام أدوار متقدم
- حماية من SQL Injection
- تشفير البيانات الحساسة
- تسجيل العمليات

### ⚡ الأداء:
- ذاكرة مؤقتة ذكية
- تحسين الاستعلامات
- معالجة متوازية
- إعادة المحاولة التلقائية

### 📊 التحليل:
- إحصائيات متقدمة
- كشف الاتجاهات
- تحليل الارتباط
- كشف القيم الشاذة

## 🧪 الاختبار

```bash
# تشغيل الاختبار الشامل
npm run test:agent

# اختبار سريع
npm run test:quick
```

```typescript
import { runSystemTests, quickTest } from '@/lib/agent';

// اختبار شامل
await runSystemTests();

// اختبار سريع
await quickTest();
```

## 📁 هيكل الملفات

```
src/lib/agent/
├── intent-classifier.ts          # تصنيف النوايا
├── synonym-processor.ts          # معالجة المرادفات
├── prompt-template-manager.ts    # قوالب البرومبت
├── rules-engine.ts              # نظام القواعد
├── database-tools.ts            # أدوات قاعدة البيانات
├── analysis-tools.ts            # أدوات التحليل
├── intelligent-agent.ts         # الوكيل الرئيسي
├── system-test.ts              # اختبارات النظام
├── intents.json                # تعريف النوايا
├── synonyms.json               # المرادفات والأنماط
├── prompt-templates.json       # قوالب البرومبت
├── rules.json                  # قواعد الأمان
└── index.ts                    # نقطة الدخول
```

## ⚙️ الإعدادات

```typescript
const agentConfig = {
  enableCaching: true,        // تفعيل الذاكرة المؤقتة
  maxRetries: 3,             // عدد المحاولات
  timeoutMs: 30000,          // مهلة الانتظار
  debugMode: false           // وضع التصحيح
};
```

## 🔧 التخصيص

### إضافة نية جديدة:
```typescript
const newIntent = {
  id: 'custom_intent',
  name: 'نية مخصصة',
  patterns: ['نمط 1', 'نمط 2'],
  // ...
};

agent.intentClassifier.addIntent(newIntent);
```

### إضافة مرادفات:
```typescript
agent.synonymProcessor.addSynonym(
  'sales_terms',
  'مبيعات',
  'تجارة'
);
```

### إضافة قالب SQL:
```typescript
const template = {
  name: 'قالب مخصص',
  base_query: 'SELECT ...',
  // ...
};

agent.promptTemplateManager.addTemplate('custom_template', template);
```

## 📚 أمثلة متقدمة

### استعلام معقد:
```typescript
const response = await agent.processQuery(
  'أريد تقرير مفصل عن أكثر 10 منتجات مبيعاً في فرع الرياض خلال الشهر الماضي مع تحليل الأرباح',
  userContext,
  tableInfo
);
```

### تحليل البيانات:
```typescript
const analysis = agent.analysisTools.analyzeData(data);
console.log(analysis.insights);
console.log(analysis.recommendations);
```

### كشف القيم الشاذة:
```typescript
const outliers = agent.analysisTools.detectOutliers(data, 'Amount');
console.log(`تم العثور على ${outliers.count} قيمة شاذة`);
```

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. إضافة الاختبارات
4. تشغيل الاختبارات
5. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 🆘 الدعم

للحصول على المساعدة:
- راجع الوثائق
- تشغيل الاختبارات
- فحص ملفات الأمثلة
- التواصل مع فريق التطوير

---

**تم تطوير هذا النظام بواسطة فريق الذكاء الاصطناعي**

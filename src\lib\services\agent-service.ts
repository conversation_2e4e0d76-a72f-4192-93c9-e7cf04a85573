import { DatabaseConnection } from '@/lib/database/types';
import { UserContext } from '@/lib/agent/rules-engine';

export interface AgentResponse {
  success: boolean;
  query?: string;
  explanation?: string;
  confidence?: number;
  results?: any[];
  error?: string;
  warnings?: string[];
  metadata?: {
    intent: string;
    processingTimeMs: number;
    rulesApplied: string[];
    templateUsed?: string;
    estimatedRows?: number;
  };
}

export interface AgentInitResponse {
  success: boolean;
  message?: string;
  error?: string;
}

export interface AgentStatusResponse {
  success: boolean;
  initialized: boolean;
  error?: string;
}

export class AgentService {
  private static instance: AgentService;

  private constructor() {}

  static getInstance(): AgentService {
    if (!AgentService.instance) {
      AgentService.instance = new AgentService();
    }
    return AgentService.instance;
  }

  /**
   * تهيئة الوكيل الذكي
   */
  async initialize(connection?: DatabaseConnection): Promise<AgentInitResponse> {
    try {
      const response = await fetch('/api/agent/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ connection }),
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('خطأ في تهيئة الوكيل:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ في الاتصال'
      };
    }
  }

  /**
   * فحص حالة الوكيل
   */
  async getStatus(): Promise<AgentStatusResponse> {
    try {
      const response = await fetch('/api/agent/initialize', {
        method: 'GET',
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('خطأ في فحص حالة الوكيل:', error);
      return {
        success: false,
        initialized: false,
        error: error instanceof Error ? error.message : 'خطأ في الاتصال'
      };
    }
  }

  /**
   * معالجة استعلام المستخدم
   */
  async processQuery(query: string, userContext?: UserContext): Promise<AgentResponse> {
    try {
      const response = await fetch('/api/agent/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query, userContext }),
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('خطأ في معالجة الاستعلام:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ في الاتصال'
      };
    }
  }
}

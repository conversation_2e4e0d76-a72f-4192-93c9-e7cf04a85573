import { IntelligentAgent } from './intelligent-agent';
import { UserContext } from './rules-engine';
import { SchemaGenerator } from './schema-generator';
import { AgentProfileGenerator } from './agent-profile-generator';

/**
 * اختبار شامل للنظام المحدث مع Schema وAgent Profile
 */
export async function runEnhancedSystemTests() {
  console.log('🧪 بدء الاختبار الشامل للنظام المحدث...\n');

  const results = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    errors: [] as string[]
  };

  try {
    // 1. اختبار Schema Generator
    console.log('📊 اختبار Schema Generator...');
    const schemaResults = await testSchemaGenerator();
    mergeResults(results, schemaResults);

    // 2. اختبار Agent Profile Generator
    console.log('🤖 اختبار Agent Profile Generator...');
    const profileResults = await testAgentProfileGenerator();
    mergeResults(results, profileResults);

    // 3. اختبار الوكيل المحدث
    console.log('🔗 اختبار الوكيل الذكي المحدث...');
    const agentResults = await testEnhancedAgent();
    mergeResults(results, agentResults);

    // 4. اختبار التكامل الكامل
    console.log('🌐 اختبار التكامل الكامل...');
    const integrationResults = await testFullIntegration();
    mergeResults(results, integrationResults);

  } catch (error) {
    console.error('❌ خطأ في تشغيل الاختبارات:', error);
    results.errors.push(`خطأ عام: ${error}`);
  }

  // طباعة النتائج النهائية
  printEnhancedResults(results);
  
  return results;
}

/**
 * اختبار Schema Generator
 */
async function testSchemaGenerator() {
  const results = { totalTests: 0, passedTests: 0, failedTests: 0, errors: [] as string[] };

  try {
    const schemaGenerator = new SchemaGenerator();

    // اختبار توليد Schema (محاكاة)
    results.totalTests++;
    try {
      console.log('  📋 اختبار توليد Schema...');
      
      // في البيئة الحقيقية سيتم الاتصال بقاعدة البيانات
      // هنا نحاكي النتيجة
      const mockSchema = {
        tables: [
          {
            name: 'tbltemp_ItemsMain',
            description: 'جدول يحتوي على تفاصيل العناصر والمنتجات في الفواتير',
            columns: [
              {
                name: 'ID',
                type: 'bigint',
                description: 'معرف فريد للسجل',
                isImportant: true,
                category: 'id' as const
              },
              {
                name: 'ItemName',
                type: 'varchar(200)',
                description: 'اسم المنتج',
                isImportant: true,
                category: 'name' as const
              },
              {
                name: 'Amount',
                type: 'numeric(18,6)',
                description: 'المبلغ الإجمالي للعملية',
                isImportant: true,
                category: 'amount' as const
              }
            ]
          }
        ],
        version: '1.0.0',
        generatedAt: new Date().toISOString(),
        metadata: {
          totalTables: 1,
          totalColumns: 3,
          importantColumns: 3
        }
      };

      // حفظ Schema تجريبي
      await schemaGenerator.saveSchemaToFile(mockSchema, 'test-schema.json');
      
      // تحميل Schema
      const loadedSchema = await schemaGenerator.loadSchemaFromFile('test-schema.json');
      
      if (loadedSchema && loadedSchema.tables.length > 0) {
        results.passedTests++;
        console.log('    ✅ توليد وحفظ وتحميل Schema نجح');
      } else {
        results.failedTests++;
        results.errors.push('فشل في تحميل Schema');
      }

    } catch (error) {
      results.failedTests++;
      results.errors.push(`خطأ في Schema Generator: ${error}`);
    }

  } catch (error) {
    results.errors.push(`خطأ في تهيئة Schema Generator: ${error}`);
  }

  return results;
}

/**
 * اختبار Agent Profile Generator
 */
async function testAgentProfileGenerator() {
  const results = { totalTests: 0, passedTests: 0, failedTests: 0, errors: [] as string[] };

  try {
    const profileGenerator = new AgentProfileGenerator();

    // اختبار توليد Agent Profile
    results.totalTests++;
    try {
      console.log('  🤖 اختبار توليد Agent Profile...');

      // Schema تجريبي
      const mockSchema = {
        tables: [
          {
            name: 'tbltemp_ItemsMain',
            description: 'جدول يحتوي على تفاصيل العناصر والمنتجات في الفواتير',
            columns: [
              {
                name: 'ItemName',
                type: 'varchar(200)',
                description: 'اسم المنتج',
                isImportant: true,
                category: 'name' as const
              },
              {
                name: 'Amount',
                type: 'numeric(18,6)',
                description: 'المبلغ الإجمالي للعملية',
                isImportant: true,
                category: 'amount' as const
              },
              {
                name: 'TheDate',
                type: 'datetime',
                description: 'تاريخ العملية',
                isImportant: true,
                category: 'date' as const
              }
            ]
          }
        ],
        version: '1.0.0',
        generatedAt: new Date().toISOString(),
        metadata: {
          totalTables: 1,
          totalColumns: 3,
          importantColumns: 3
        }
      };

      const profile = await profileGenerator.generateAgentProfile('tbltemp_ItemsMain', mockSchema);
      
      if (profile && profile.use_cases.length > 0) {
        results.passedTests++;
        console.log(`    ✅ توليد Agent Profile نجح: ${profile.use_cases.length} حالة استخدام`);
        
        // حفظ Profile
        await profileGenerator.saveProfileToFile(profile, 'test-agent-profile.json');
      } else {
        results.failedTests++;
        results.errors.push('فشل في توليد Agent Profile');
      }

    } catch (error) {
      results.failedTests++;
      results.errors.push(`خطأ في Agent Profile Generator: ${error}`);
    }

  } catch (error) {
    results.errors.push(`خطأ في تهيئة Agent Profile Generator: ${error}`);
  }

  return results;
}

/**
 * اختبار الوكيل المحدث
 */
async function testEnhancedAgent() {
  const results = { totalTests: 0, passedTests: 0, failedTests: 0, errors: [] as string[] };

  try {
    const agent = new IntelligentAgent({
      debugMode: true,
      enableCaching: true,
      maxRetries: 2,
      timeoutMs: 10000
    });

    const userContext: UserContext = {
      userId: 'test_user',
      role: 'manager',
      branchId: 'الرياض',
      permissions: ['read', 'write']
    };

    const tableInfo = `
      جدول tbltemp_ItemsMain:
      - ItemName: اسم المنتج
      - Amount: المبلغ
      - TheDate: التاريخ
    `;

    // اختبار الاستعلامات مع النظام المحدث
    const testQueries = [
      'إجمالي المبيعات اليوم',
      'أكثر 5 منتجات مبيعاً',
      'أفضل العملاء في فرع الرياض'
    ];

    for (const query of testQueries) {
      results.totalTests++;
      try {
        console.log(`  📝 اختبار: "${query}"`);
        
        const response = await agent.processQuery(query, userContext, tableInfo);
        
        if (response.success) {
          results.passedTests++;
          console.log(`    ✅ نجح - النية: ${response.metadata.intent}`);
        } else {
          results.failedTests++;
          results.errors.push(`فشل "${query}": ${response.error}`);
        }
      } catch (error) {
        results.failedTests++;
        results.errors.push(`خطأ في "${query}": ${error}`);
      }
    }

    // اختبار الوصول إلى Schema وProfiles
    results.totalTests++;
    try {
      const schema = agent.getDatabaseSchema();
      const profiles = agent.getAllAgentProfiles();
      
      console.log(`  📊 Schema: ${schema ? 'متاح' : 'غير متاح'}`);
      console.log(`  🤖 Profiles: ${profiles.size} ملف`);
      
      results.passedTests++;
      console.log('    ✅ الوصول إلى Schema وProfiles نجح');
    } catch (error) {
      results.failedTests++;
      results.errors.push(`خطأ في الوصول إلى Schema/Profiles: ${error}`);
    }

  } catch (error) {
    results.errors.push(`خطأ في تهيئة الوكيل المحدث: ${error}`);
  }

  return results;
}

/**
 * اختبار التكامل الكامل
 */
async function testFullIntegration() {
  const results = { totalTests: 0, passedTests: 0, failedTests: 0, errors: [] as string[] };

  try {
    console.log('  🌐 اختبار التدفق الكامل من Schema إلى النتيجة...');

    results.totalTests++;
    
    // محاكاة التدفق الكامل
    const agent = new IntelligentAgent({ debugMode: true });
    
    // انتظار تهيئة النظام
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const userContext: UserContext = {
      userId: 'integration_test',
      role: 'manager',
      branchId: 'الرياض',
      permissions: ['read', 'write']
    };

    const complexQuery = 'أريد تقرير مفصل عن أكثر 10 منتجات مبيعاً في فرع الرياض خلال الشهر الماضي';
    
    const response = await agent.processQuery(complexQuery, userContext, '');
    
    // فحص أن جميع المكونات عملت
    const hasSchema = agent.getDatabaseSchema() !== null;
    const hasProfiles = agent.getAllAgentProfiles().size > 0;
    const hasResponse = response.success || response.error;
    const hasMetadata = response.metadata && response.metadata.intent;
    
    if (hasSchema && hasProfiles && hasResponse && hasMetadata) {
      results.passedTests++;
      console.log('    ✅ التكامل الكامل نجح');
      console.log(`      - Schema: ${hasSchema ? '✓' : '✗'}`);
      console.log(`      - Profiles: ${hasProfiles ? '✓' : '✗'}`);
      console.log(`      - Response: ${hasResponse ? '✓' : '✗'}`);
      console.log(`      - Metadata: ${hasMetadata ? '✓' : '✗'}`);
    } else {
      results.failedTests++;
      results.errors.push('التكامل الكامل فشل - بعض المكونات لا تعمل');
    }

  } catch (error) {
    results.failedTests++;
    results.errors.push(`خطأ في اختبار التكامل: ${error}`);
  }

  return results;
}

/**
 * دمج نتائج الاختبارات
 */
function mergeResults(target: any, source: any): void {
  target.totalTests += source.totalTests;
  target.passedTests += source.passedTests;
  target.failedTests += source.failedTests;
  target.errors.push(...source.errors);
}

/**
 * طباعة النتائج المحسنة
 */
function printEnhancedResults(results: any) {
  console.log('\n' + '='.repeat(80));
  console.log('📊 نتائج الاختبار الشامل للنظام المحدث');
  console.log('='.repeat(80));
  
  console.log(`📈 إجمالي الاختبارات: ${results.totalTests}`);
  console.log(`✅ نجح: ${results.passedTests} (${((results.passedTests / results.totalTests) * 100).toFixed(1)}%)`);
  console.log(`❌ فشل: ${results.failedTests} (${((results.failedTests / results.totalTests) * 100).toFixed(1)}%)`);
  
  if (results.errors.length > 0) {
    console.log('\n🚨 الأخطاء المكتشفة:');
    results.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }
  
  const successRate = (results.passedTests / results.totalTests) * 100;
  if (successRate >= 90) {
    console.log('\n🎉 النظام المحدث يعمل بشكل ممتاز!');
  } else if (successRate >= 75) {
    console.log('\n👍 النظام المحدث يعمل بشكل جيد مع بعض التحسينات المطلوبة.');
  } else {
    console.log('\n⚠️ النظام المحدث يحتاج إلى تحسينات كبيرة.');
  }
  
  console.log('\n🆕 المميزات الجديدة:');
  console.log('   ✓ Schema Generator - توليد هيكل الجداول تلقائياً');
  console.log('   ✓ Agent Profile Generator - توليد ملفات التوصيف الذكية');
  console.log('   ✓ Enhanced Prompt Templates - قوالب محسنة مع Schema');
  console.log('   ✓ Intelligent Table Detection - اكتشاف الجداول المناسبة');
  console.log('   ✓ Smart Column Filtering - التركيز على الأعمدة المهمة');
  
  console.log('='.repeat(80));
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  runEnhancedSystemTests().catch(console.error);
}

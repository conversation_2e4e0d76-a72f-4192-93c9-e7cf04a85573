import { NextRequest, NextResponse } from 'next/server';
import { IntelligentAgent } from '@/lib/agent/intelligent-agent';
import { DatabaseConnection } from '@/lib/database/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { connection } = body;

    // إنشاء مثيل من الوكيل الذكي (التهيئة تحدث تلقائياً في الكونستركتور)
    const agent = new IntelligentAgent({
      enableCaching: true,
      maxRetries: 3,
      timeoutMs: 30000,
      debugMode: true
    });

    // إذا تم توفير معلومات اتصال، يمكن حفظها للاستخدام لاحقاً
    if (connection) {
      // يمكن إضافة منطق لحفظ معلومات الاتصال هنا إذا لزم الأمر
      console.log('تم استلام معلومات الاتصال:', connection.host);
    }

    return NextResponse.json({
      success: true,
      message: 'تم تهيئة الوكيل الذكي بنجاح'
    });

  } catch (error) {
    console.error('خطأ في تهيئة الوكيل:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // فحص إمكانية تهيئة الوكيل
    const agent = new IntelligentAgent({
      enableCaching: true,
      maxRetries: 3,
      timeoutMs: 30000,
      debugMode: false
    });

    // الوكيل يتم تهيئته تلقائياً في الكونستركتور
    return NextResponse.json({
      success: true,
      initialized: true
    });

  } catch (error) {
    console.error('خطأ في فحص حالة الوكيل:', error);

    return NextResponse.json(
      {
        success: false,
        initialized: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

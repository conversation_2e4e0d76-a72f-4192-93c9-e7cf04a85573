import { IntelligentAgent } from './intelligent-agent';
import { UserContext } from './rules-engine';

/**
 * اختبار شامل للوكيل الذكي
 */
async function testIntelligentAgent() {
  console.log('🧠 بدء اختبار الوكيل الذكي الشامل...\n');

  // إنشاء الوكيل مع تفعيل وضع التصحيح
  const agent = new IntelligentAgent({
    debugMode: true,
    enableCaching: true,
    maxRetries: 2,
    timeoutMs: 10000
  });

  // سياق مستخدم تجريبي
  const userContext: UserContext = {
    userId: 'test_user_001',
    role: 'manager',
    branchId: 'الرياض',
    permissions: ['read', 'write']
  };

  // معلومات الجداول (محاكاة)
  const tableInfo = `
    جدول tbltemp_ItemsMain:
    - ItemName: اسم المنتج
    - Quantity: الكمية
    - Amount: المبلغ
    - TheDate: التاريخ
    - ClientName: اسم العميل
    - BranchName: اسم الفرع
    - DocumentName: نوع المستند
  `;

  // اختبارات مختلفة
  const testCases = [
    {
      query: 'إجمالي المبيعات اليوم',
      description: 'استعلام بسيط عن المبيعات اليومية',
      expectedIntent: 'sales_total'
    },
    {
      query: 'أكثر 5 منتجات مبيعاً هذا الشهر',
      description: 'استعلام عن أفضل المنتجات مع تحديد العدد',
      expectedIntent: 'top_products'
    },
    {
      query: 'أفضل العملاء في فرع الرياض',
      description: 'تحليل العملاء مع فلتر الفرع',
      expectedIntent: 'customer_analysis'
    },
    {
      query: 'حالة مخزون الهواتف الذكية',
      description: 'استعلام عن المخزون لمنتج محدد',
      expectedIntent: 'inventory_status'
    },
    {
      query: 'مشتريات من مورد الشركة الذهبية الشهر الماضي',
      description: 'تحليل المشتريات مع فلتر المورد والتاريخ',
      expectedIntent: 'purchase_analysis'
    },
    {
      query: 'أرباح الربع الأول من هذه السنة',
      description: 'تقرير مالي لفترة محددة',
      expectedIntent: 'financial_reports'
    },
    {
      query: 'مقارنة أداء فرع الرياض مع فرع جدة',
      description: 'مقارنة بين الفروع',
      expectedIntent: 'branch_performance'
    },
    {
      query: 'اتجاه المبيعات الشهرية لهذه السنة',
      description: 'تحليل زمني للمبيعات',
      expectedIntent: 'time_analysis'
    }
  ];

  let successCount = 0;
  let totalTests = testCases.length;

  console.log(`📊 تشغيل ${totalTests} اختبار...\n`);

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`🧪 اختبار ${i + 1}/${totalTests}: ${testCase.description}`);
    console.log(`   الاستعلام: "${testCase.query}"`);

    try {
      const startTime = Date.now();
      const response = await agent.processQuery(
        testCase.query,
        userContext,
        tableInfo
      );
      const processingTime = Date.now() - startTime;

      if (response.success) {
        console.log(`   ✅ نجح الاستعلام`);
        console.log(`   🎯 النية: ${response.metadata.intent}`);
        console.log(`   📝 الاستعلام المولد: ${response.query?.substring(0, 100)}...`);
        console.log(`   💡 الشرح: ${response.explanation?.substring(0, 100)}...`);
        console.log(`   🎲 مستوى الثقة: ${((response.confidence || 0) * 100).toFixed(1)}%`);
        console.log(`   ⚡ وقت المعالجة: ${processingTime}ms`);
        console.log(`   📊 عدد النتائج: ${response.results?.length || 0}`);
        
        if (response.warnings && response.warnings.length > 0) {
          console.log(`   ⚠️ تحذيرات: ${response.warnings.join(', ')}`);
        }

        successCount++;
      } else {
        console.log(`   ❌ فشل الاستعلام: ${response.error}`);
        if (response.warnings && response.warnings.length > 0) {
          console.log(`   ⚠️ تحذيرات: ${response.warnings.join(', ')}`);
        }
      }

    } catch (error) {
      console.log(`   💥 خطأ في الاختبار: ${error}`);
    }

    console.log('   ' + '─'.repeat(80));
  }

  console.log(`\n📈 نتائج الاختبار:`);
  console.log(`   ✅ نجح: ${successCount}/${totalTests} (${((successCount/totalTests) * 100).toFixed(1)}%)`);
  console.log(`   ❌ فشل: ${totalTests - successCount}/${totalTests}`);

  // اختبار الأداء
  await performanceTest(agent, userContext, tableInfo);

  // اختبار الأمان
  await securityTest(agent, userContext, tableInfo);
}

/**
 * اختبار الأداء
 */
async function performanceTest(agent: IntelligentAgent, userContext: UserContext, tableInfo: string) {
  console.log(`\n⚡ اختبار الأداء...`);

  const testQuery = 'إجمالي المبيعات اليوم';
  const iterations = 10;
  const times: number[] = [];

  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now();
    await agent.processQuery(testQuery, userContext, tableInfo);
    const endTime = Date.now();
    times.push(endTime - startTime);
  }

  const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
  const minTime = Math.min(...times);
  const maxTime = Math.max(...times);

  console.log(`   📊 ${iterations} استعلام متتالي`);
  console.log(`   ⚡ متوسط الوقت: ${avgTime.toFixed(2)}ms`);
  console.log(`   🚀 أسرع وقت: ${minTime}ms`);
  console.log(`   🐌 أبطأ وقت: ${maxTime}ms`);
  console.log(`   📈 المعدل: ${(1000 / avgTime).toFixed(0)} استعلام/ثانية`);

  // اختبار الذاكرة المؤقتة
  const stats = agent.getPerformanceStats();
  console.log(`   💾 حجم الذاكرة المؤقتة: ${stats.cacheSize} عنصر`);
}

/**
 * اختبار الأمان
 */
async function securityTest(agent: IntelligentAgent, userContext: UserContext, tableInfo: string) {
  console.log(`\n🔒 اختبار الأمان...`);

  const maliciousQueries = [
    "'; DROP TABLE tbltemp_ItemsMain; --",
    "UNION SELECT * FROM users",
    "DELETE FROM tbltemp_ItemsMain",
    "UPDATE tbltemp_ItemsMain SET Amount = 0",
    "INSERT INTO tbltemp_ItemsMain VALUES (1,2,3)",
    "EXEC xp_cmdshell 'dir'",
    "/* comment */ SELECT * FROM tbltemp_ItemsMain"
  ];

  let blockedCount = 0;

  for (const maliciousQuery of maliciousQueries) {
    console.log(`   🧪 اختبار: ${maliciousQuery.substring(0, 50)}...`);
    
    try {
      const response = await agent.processQuery(maliciousQuery, userContext, tableInfo);
      
      if (!response.success) {
        console.log(`   ✅ تم حظر الاستعلام الخبيث`);
        blockedCount++;
      } else {
        console.log(`   ❌ لم يتم حظر الاستعلام الخبيث!`);
      }
    } catch (error) {
      console.log(`   ✅ تم حظر الاستعلام الخبيث (استثناء)`);
      blockedCount++;
    }
  }

  console.log(`   🛡️ تم حظر ${blockedCount}/${maliciousQueries.length} استعلام خبيث`);
  console.log(`   📊 معدل الحماية: ${((blockedCount/maliciousQueries.length) * 100).toFixed(1)}%`);
}

/**
 * اختبار أدوار المستخدمين المختلفة
 */
async function roleBasedTest(agent: IntelligentAgent, tableInfo: string) {
  console.log(`\n👥 اختبار أدوار المستخدمين...`);

  const roles = ['admin', 'manager', 'employee', 'viewer'] as const;
  const testQuery = 'إجمالي المبيعات مع أسماء العملاء';

  for (const role of roles) {
    const userContext: UserContext = {
      userId: `test_${role}`,
      role: role,
      branchId: 'الرياض',
      permissions: role === 'admin' ? ['read', 'write', 'delete', 'admin'] : ['read']
    };

    console.log(`   👤 اختبار دور: ${role}`);
    
    try {
      const response = await agent.processQuery(testQuery, userContext, tableInfo);
      
      if (response.success) {
        console.log(`   ✅ نجح الاستعلام للدور ${role}`);
      } else {
        console.log(`   ❌ فشل الاستعلام للدور ${role}: ${response.error}`);
      }
    } catch (error) {
      console.log(`   💥 خطأ للدور ${role}: ${error}`);
    }
  }
}

// تشغيل الاختبارات
if (require.main === module) {
  (async () => {
    await testIntelligentAgent();
  })();
}

export { testIntelligentAgent, performanceTest, securityTest, roleBasedTest };

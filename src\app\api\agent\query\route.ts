import { NextRequest, NextResponse } from 'next/server';
import { IntelligentAgent, AgentResponse } from '@/lib/agent/intelligent-agent';
import { UserContext } from '@/lib/agent/rules-engine';
import { SchemaManager } from '@/lib/database/schema-manager';
import { DatabaseSchema } from '@/lib/database/types';

// دالة مساعدة لتحويل Schema إلى معلومات الجداول
function generateTableInfo(schema: DatabaseSchema): string {
  if (!schema.tables || schema.tables.length === 0) {
    return 'لا توجد جداول متاحة';
  }

  let tableInfo = '';

  schema.tables.forEach(table => {
    tableInfo += `جدول ${table.name}:\n`;

    // إضافة الأعمدة المهمة
    const importantColumns = table.columns.filter(col =>
      col.name.toLowerCase().includes('name') ||
      col.name.toLowerCase().includes('amount') ||
      col.name.toLowerCase().includes('quantity') ||
      col.name.toLowerCase().includes('price') ||
      col.name.toLowerCase().includes('date') ||
      col.name.toLowerCase().includes('total') ||
      col.name.toLowerCase().includes('client') ||
      col.name.toLowerCase().includes('supplier') ||
      col.name.toLowerCase().includes('distributor') ||
      col.name.toLowerCase().includes('document') ||
      col.name.toLowerCase().includes('item') ||
      col.name.toLowerCase().includes('branch') ||
      col.name.toLowerCase().includes('store')
    );

    importantColumns.forEach(col => {
      tableInfo += `  - ${col.name}: ${col.type}\n`;
    });

    tableInfo += `  عدد الصفوف: ${table.rowCount || 0}\n\n`;
  });

  return tableInfo;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, userContext } = body;

    if (!query) {
      return NextResponse.json(
        { success: false, error: 'Query is required' },
        { status: 400 }
      );
    }

    console.log('🤖 بدء معالجة الاستعلام:', query);

    // تحميل Schema من الملف
    const schemaManager = SchemaManager.getInstance();
    let schema = await schemaManager.loadSchema();

    if (!schema) {
      // محاولة تحميل من database-schema.json
      const fs = require('fs');
      const path = require('path');
      try {
        const schemaPath = path.join(process.cwd(), 'data', 'database-schema.json');
        const schemaData = fs.readFileSync(schemaPath, 'utf8');
        schema = JSON.parse(schemaData) as DatabaseSchema;
      } catch (error) {
        console.error('فشل في تحميل Schema:', error);
        return NextResponse.json(
          { success: false, error: 'فشل في تحميل بنية قاعدة البيانات' },
          { status: 500 }
        );
      }
    }

    // إنشاء مثيل من الوكيل الذكي
    const agent = new IntelligentAgent({
      enableCaching: true,
      maxRetries: 3,
      timeoutMs: 30000,
      debugMode: true
    });

    // تحويل Schema إلى معلومات الجداول
    const tableInfo = generateTableInfo(schema);
    console.log('📊 معلومات الجداول المحملة:', tableInfo.substring(0, 200) + '...');

    // تنفيذ الاستعلام
    const response: AgentResponse = await agent.processQuery(query, userContext || {
      userId: 'user_001',
      role: 'manager',
      branchId: undefined, // لا نحدد فرع افتراضي - يجب أن يحدده المستخدم
      permissions: ['read', 'write']
    }, tableInfo);

    return NextResponse.json(response);

  } catch (error) {
    console.error('خطأ في معالجة الاستعلام:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

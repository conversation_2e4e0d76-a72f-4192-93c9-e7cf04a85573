// وكيل SQL محسن للسرعة - يقلل عدد استدعاءات API

import { IntelligentSummaryGenerator } from './intelligent-summary';
import { ContextUnderstanding } from './context-understanding';
import { AIProviderManager } from './ai-provider-manager';

export interface SQLResult {
  query: string;
  explanation: string;
  confidence: number;
  reasoning?: string;
}

export class FastSQLAgent {
  private llmClient: any;
  private summaryGenerator: IntelligentSummaryGenerator;
  private contextUnderstanding: ContextUnderstanding;
  private schema: any;

  constructor(schema: any, llmClient: any) {
    this.schema = schema;
    this.llmClient = llmClient;
    this.summaryGenerator = new IntelligentSummaryGenerator(llmClient);
    this.contextUnderstanding = new ContextUnderstanding(llmClient);
  }

  /**
   * توليد SQL بطريقة سريعة (استدعاء واحد فقط)
   */
  async generateSQL(userQuestion: string): Promise<SQLResult> {
    console.log('⚡ بدء التحليل السريع للسؤال:', userQuestion);

    // فحص إذا كان السؤال يتضمن مقارنة
    const isComparison = this.detectComparison(userQuestion);
    const comparisonItems = isComparison ? this.extractComparisonItems(userQuestion) : [];

    // تحسين السياق أولاً (استدعاء واحد)
    const contextualQuery = await this.contextUnderstanding.analyzeContext(userQuestion);
    const enhancedQuestion = contextualQuery.enhancedQuery;

    console.log('🔍 السؤال المحسن:', enhancedQuestion);
    if (isComparison) {
      console.log('🔄 تم اكتشاف مقارنة - العناصر:', comparisonItems);
    }

    // توليد SQL مباشر في استدعاء واحد
    let result: SQLResult;

    if (isComparison && comparisonItems.length > 0) {
      // محاولة حل سريع للمقارنات
      result = await this.generateComparisonSQL(enhancedQuestion, comparisonItems);
    } else {
      result = await this.generateSQLDirect(enhancedQuestion, isComparison, comparisonItems);
    }

    return result;
  }

  /**
   * اكتشاف إذا كان السؤال يتضمن مقارنة
   */
  private detectComparison(question: string): boolean {
    const comparisonKeywords = [
      'قارن', 'مقارنة', 'بين', 'مقابل', 'ضد', 'أفضل من', 'أكثر من', 'أقل من',
      'الفرق بين', 'الاختلاف', 'التباين', 'versus', 'vs', 'compare'
    ];

    return comparisonKeywords.some(keyword =>
      question.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * استخراج العناصر المراد مقارنتها من السؤال
   */
  private extractComparisonItems(question: string): string[] {
    const items: string[] = [];

    // البحث عن أنماط شائعة
    const patterns = [
      /بين\s+(.+?)\s+و\s*(.+?)(?:\s|$)/g,  // "بين X و Y"
      /(.+?)\s+مقابل\s+(.+?)(?:\s|$)/g,     // "X مقابل Y"
      /(.+?)\s+ضد\s+(.+?)(?:\s|$)/g,        // "X ضد Y"
      /مقارنة\s+(.+?)\s+و\s*(.+?)(?:\s|$)/g // "مقارنة X و Y"
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(question)) !== null) {
        if (match[1]) {
          // تنظيف العنصر الأول (إزالة "مبيعات" لكن الاحتفاظ بـ "ال")
          let item1 = match[1].trim().replace(/مبيعات\s+/g, '');
          items.push(item1);
        }
        if (match[2]) {
          // تنظيف العنصر الثاني (إزالة "مبيعات" لكن الاحتفاظ بـ "ال")
          let item2 = match[2].trim().replace(/مبيعات\s+/g, '');
          items.push(item2);
        }
      }
    }

    return items.filter(item => item.length > 0);
  }

  /**
   * توليد SQL خاص للمقارنات (حل سريع) باستخدام الجداول الحقيقية
   */
  private async generateComparisonSQL(_question: string, comparisonItems: string[]): Promise<SQLResult> {
    console.log('🔄 توليد استعلام مقارنة سريع للعناصر:', comparisonItems);

    // استخدام دالة الاستعلام الاحتياطي التي تستخدم الجداول الحقيقية
    return this.generateFallbackQuery('comparison', comparisonItems);
  }

  /**
   * توليد SQL مباشر في استدعاء واحد
   */
  private async generateSQLDirect(question: string, isComparison: boolean = false, comparisonItems: string[] = []): Promise<SQLResult> {
    const schemaDescription = this.generateSchemaDescription();

    console.log('🔍 Schema Description:', schemaDescription);
    console.log('🎯 Question:', question);

    const prompt = `🚀 **مولد SQL سريع ومحسن**

**السؤال:** "${question}"

**قاعدة البيانات:** SQL Server (استخدم TOP بدلاً من LIMIT)

**الجداول المتاحة:**
${schemaDescription}

**قواعد مهمة:**
- استخدم أسماء الجداول والأعمدة الموجودة فعلياً فقط من القائمة أعلاه
- SQL Server: استخدم TOP بدلاً من LIMIT
- الجدول الرئيسي: tbltemp_ItemsMain (يحتوي على تفاصيل المنتجات والمبيعات)
- للمنتجات: استخدم ItemID و ItemName من tbltemp_ItemsMain
- للعملاء: استخدم ClientID و ClientName من tbltemp_ItemsMain
- للكميات: استخدم Quantity من tbltemp_ItemsMain
- للمبالغ: استخدم Amount من tbltemp_ItemsMain
- للمبيعات: ابحث في DocumentName عن كلمة "مبيعات" أو استخدم WHERE DocumentName LIKE '%مبيعات%'
- لا تستخدم أسماء جداول مثل items, products, customers (غير موجودة)
- لا تستخدم JOIN إلا إذا كان ضرورياً جداً

**أمثلة:**
- "أكثر المنتجات مبيعاً" → SELECT TOP 10 ItemID, ItemName, SUM(Quantity) as TotalQuantity FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مبيعات%' GROUP BY ItemID, ItemName ORDER BY SUM(Quantity) DESC
- "عدد العملاء" → SELECT COUNT(DISTINCT ClientID) FROM tbltemp_ItemsMain WHERE ClientID IS NOT NULL

**تحليل السؤال الحالي:**
${this.analyzeQuestionContext(question)}

JSON فقط:
{"query": "SELECT ..."}`;

    console.log('📤 Sending prompt to LLM:', prompt);

    try {
      console.log('📤 إرسال الطلب للنموذج اللغوي...');
      const response = await this.llmClient.generateContent(prompt);
      console.log('📥 استجابة النموذج اللغوي:', response);

      const result = this.parseJSONResponse(response);
      console.log('📝 الاستعلام المولد:', result.query);

      // التحقق من صحة النتيجة
      if (!result.query || result.query.trim() === '') {
        console.warn('⚠️ الاستعلام فارغ، استخدام النظام الاحتياطي');
        throw new Error('لم يتم الحصول على استعلام من AI');
      }

      return {
        query: result.query,
        explanation: result.explanation || 'استعلام مولد بواسطة النظام السريع',
        confidence: result.confidence || 0.8,
        reasoning: result.reasoning || 'توليد مباشر'
      };

    } catch (error) {
      console.error('❌ خطأ في توليد SQL السريع:', error);
      console.log('🔄 التبديل للنظام الاحتياطي...');

      // استعلام افتراضي حسب نوع السؤال باستخدام الجداول الحقيقية
      if (isComparison) {
        return this.generateFallbackQuery('comparison', comparisonItems);
      } else {
        return this.generateFallbackQuery('general');
      }
    }
  }

  /**
   * تحليل السؤال لفهم السياق
   */
  private analyzeQuestionContext(question: string): string {
    const analysis = [];

    // استخراج الأرقام من السؤال
    const numberMatch = question.match(/(\d+)/);
    if (numberMatch) {
      analysis.push(`- تم العثور على رقم: ${numberMatch[1]} (سيتم استخدامه في TOP)`);
    }

    if (question.includes('مبيعات') || question.includes('بيع')) {
      analysis.push('- السؤال يتعلق بالمبيعات، سيتم فلترة DocumentName');
    }

    if (question.includes('منتج') || question.includes('أكثر') || question.includes('أفضل')) {
      analysis.push('- السؤال يتطلب تجميع حسب المنتجات');
    }

    if (question.includes('عميل') || question.includes('زبون')) {
      analysis.push('- السؤال يتعلق بالعملاء');
    }

    if (question.includes('كمية') || question.includes('عدد')) {
      analysis.push('- السؤال يتطلب حساب الكميات');
    }

    if (question.includes('جميع') || question.includes('كل')) {
      analysis.push('- السؤال يطلب عرض جميع البيانات (بدون فلترة مبيعات)');
    }

    return analysis.length > 0 ? analysis.join('\n') : '- سؤال عام';
  }

  /**
   * إنشاء شرط LIKE ذكي يتعامل مع أداة التعريف "ال"
   */
  private createSmartLikeCondition(columnName: string, searchTerm: string): string {
    const cleanTerm = searchTerm.trim();

    // إذا كان المصطلح يبدأ بـ "ال"
    if (cleanTerm.startsWith('ال')) {
      const withoutAl = cleanTerm.substring(2); // إزالة "ال"
      return `(${columnName} LIKE '%${cleanTerm}%' OR ${columnName} LIKE '%${withoutAl}%')`;
    } else {
      // إذا لم يبدأ بـ "ال"، ابحث عن الاثنين
      return `(${columnName} LIKE '%${cleanTerm}%' OR ${columnName} LIKE '%ال${cleanTerm}%')`;
    }
  }

  /**
   * توليد استعلام احتياطي باستخدام الجداول الحقيقية
   */
  public generateFallbackQuery(type: 'comparison' | 'general', _items?: string[]): SQLResult {
    console.log('🔄 توليد استعلام احتياطي نوع:', type);
    console.log('📊 Schema متاح:', !!this.schema);
    console.log('📋 عدد الجداول:', this.schema?.tables?.length || 0);

    if (!this.schema || !this.schema.tables || this.schema.tables.length === 0) {
      console.warn('⚠️ لا توجد جداول متاحة');
      return {
        query: "SELECT 'لا توجد جداول متاحة' as message",
        explanation: 'لا توجد جداول متاحة في قاعدة البيانات',
        confidence: 0.1,
        reasoning: 'لا توجد بيانات'
      };
    }

    // البحث عن جدول الفواتير الرئيسي
    console.log('🔍 البحث عن جدول الفواتير...');
    console.log('📋 أسماء الجداول المتاحة:', this.schema.tables.map((t: any) => t.name));

    const invoiceTable = this.schema.tables.find((t: { name: string }) =>
      t.name.toLowerCase().includes('invoice') ||
      t.name.toLowerCase().includes('فاتورة') ||
      t.name.includes('Inv_MainInvoice') ||
      t.name.includes('ItemsMain')
    );

    if (!invoiceTable) {
      console.warn('⚠️ لم يتم العثور على جدول فواتير، استخدام أول جدول متاح');
      const firstTable = this.schema.tables[0];
      console.log('📋 استخدام الجدول:', firstTable.name);
      return {
        query: `SELECT COUNT(*) as total_count FROM ${firstTable.name}`,
        explanation: `عدد السجلات في جدول ${firstTable.name}`,
        confidence: 0.5,
        reasoning: 'استعلام عام على أول جدول متاح'
      };
    }

    console.log('✅ تم العثور على جدول:', invoiceTable.name);

    // البحث عن أعمدة مهمة في جدول الفواتير مع التمييز بين ID والاسم
    const quantityColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('quantity') ||
      c.name.toLowerCase().includes('كمية') ||
      c.name === 'Quantity'
    );

    // البحث عن عمود اسم المنتج أولاً (للعرض)
    const itemNameColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('itemname') ||
      c.name.toLowerCase().includes('productname') ||
      c.name.toLowerCase().includes('منتج') && c.name.toLowerCase().includes('اسم') ||
      c.name === 'ItemName'
    );

    // البحث عن عمود ID المنتج (للتجميع)
    const itemIdColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('itemid') ||
      c.name.toLowerCase().includes('productid') ||
      c.name === 'ItemID'
    );

    // البحث عن عمود اسم العميل أولاً (للعرض)
    const clientNameColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('clientname') ||
      c.name.toLowerCase().includes('customername') ||
      c.name.toLowerCase().includes('suppliername') ||
      c.name === 'ClientName' || c.name === 'SupplierName'
    );

    // البحث عن عمود ID العميل (للتجميع)
    const clientIdColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('clientid') ||
      c.name.toLowerCase().includes('customerid') ||
      c.name === 'ClientID'
    );

    const amountColumn = invoiceTable.columns.find((c: { name: string }) =>
      c.name.toLowerCase().includes('amount') ||
      c.name.toLowerCase().includes('total') ||
      c.name.toLowerCase().includes('مبلغ') ||
      c.name === 'TotalAmount'
    );

    // توليد استعلام حسب النوع مع اختيار الأعمدة المناسبة
    if (type === 'comparison' && quantityColumn && (itemNameColumn || itemIdColumn)) {
      // للمقارنات: استخدم ItemID للتجميع مع ItemName للعرض إذا كان متوفراً
      const groupByColumn = itemIdColumn || itemNameColumn;
      const selectColumns = itemNameColumn ?
        `${itemIdColumn!.name}, ${itemNameColumn.name}` :
        `${groupByColumn!.name}`;

      // استخراج الرقم من items إذا وُجد
      const numberMatch = _items && _items.length > 0 ? _items[0].match(/(\d+)/) : null;
      const limit = numberMatch ? parseInt(numberMatch[1]) : 10;

      return {
        query: `SELECT TOP ${limit} ${selectColumns}, SUM(${quantityColumn.name}) as TotalQuantity, SUM(${amountColumn?.name || 'Amount'}) as TotalAmount
                FROM ${invoiceTable.name}
                WHERE ${quantityColumn.name} IS NOT NULL
                  AND ${groupByColumn!.name} IS NOT NULL
                  AND DocumentName LIKE '%مبيعات%'
                GROUP BY ${itemIdColumn ? `${itemIdColumn.name}, ${itemNameColumn?.name || itemIdColumn.name}` : groupByColumn!.name}
                ORDER BY TotalQuantity DESC`,
        explanation: `أكثر ${limit} منتجات مبيعاً حسب الكمية مع فلترة المبيعات`,
        confidence: 0.9,
        reasoning: 'استعلام مقارنة محسن باستخدام الجداول الحقيقية مع فلترة المبيعات'
      };
    } else if (clientIdColumn) {
      // لعد العملاء: استخدم ID للدقة مع فلترة المبيعات
      return {
        query: `SELECT COUNT(DISTINCT ${clientIdColumn.name}) as customer_count
                FROM ${invoiceTable.name}
                WHERE ${clientIdColumn.name} IS NOT NULL
                  AND DocumentName LIKE '%مبيعات%'`,
        explanation: 'عدد العملاء المختلفين في المبيعات',
        confidence: 0.8,
        reasoning: 'عد العملاء باستخدام الجداول الحقيقية مع فلترة المبيعات'
      };
    } else if (clientNameColumn && amountColumn) {
      // لأكثر العملاء شراءً: استخدم اسم العميل للعرض مع فلترة المبيعات
      return {
        query: `SELECT TOP 10 ${clientNameColumn.name}, SUM(${amountColumn.name}) as TotalAmount
                FROM ${invoiceTable.name}
                WHERE ${amountColumn.name} IS NOT NULL
                  AND ${clientNameColumn.name} IS NOT NULL
                  AND DocumentName LIKE '%مبيعات%'
                GROUP BY ${clientNameColumn.name}
                ORDER BY TotalAmount DESC`,
        explanation: 'أكثر العملاء شراءً حسب المبلغ مع فلترة المبيعات',
        confidence: 0.8,
        reasoning: 'ترتيب العملاء حسب المبيعات مع فلترة المبيعات'
      };
    } else if (amountColumn) {
      return {
        query: `SELECT SUM(${amountColumn.name}) as TotalSales, COUNT(*) as TransactionCount
                FROM ${invoiceTable.name}
                WHERE ${amountColumn.name} IS NOT NULL
                  AND DocumentName LIKE '%مبيعات%'`,
        explanation: 'إجمالي المبيعات وعدد المعاملات',
        confidence: 0.8,
        reasoning: 'حساب إجمالي المبيعات مع فلترة المبيعات'
      };
    } else {
      return {
        query: `SELECT COUNT(*) as total_records FROM ${invoiceTable.name}`,
        explanation: `عدد السجلات في جدول ${invoiceTable.name}`,
        confidence: 0.6,
        reasoning: 'استعلام عام'
      };
    }
  }

  /**
   * توليد وصف مبسط لبنية قاعدة البيانات
   */
  private generateSchemaDescription(): string {
    if (!this.schema || !this.schema.tables) {
      return 'لا توجد جداول متاحة';
    }

    // وصف مفصل للجداول والأعمدة المهمة
    let description = '';

    for (const table of this.schema.tables) {
      description += `\n**${table.name}:**\n`;

      // إضافة أهم الأعمدة (أول 10 أعمدة)
      const importantColumns = table.columns.slice(0, 10);
      for (const column of importantColumns) {
        description += `  - ${column.name} (${column.type})\n`;
      }
    }

    return description;
  }

  /**
   * تحليل استجابة JSON مبسط
   */
  private parseJSONResponse(response: string): any {
    try {
      return JSON.parse(response);
    } catch {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch {
          // فشل
        }
      }
      return { query: "SELECT * FROM items LIMIT 10" };
    }
  }

  /**
   * توليد ملخص ذكي للنتائج
   */
  async generateIntelligentSummary(
    data: any[],
    originalQuery: string,
    context?: any
  ): Promise<any> {
    console.log('📊 توليد ملخص ذكي للنتائج...');

    return await this.summaryGenerator.generateIntelligentSummary(
      data,
      originalQuery,
      context
    );
  }

  /**
   * تحديث بنية قاعدة البيانات
   */
  updateSchema(newSchema: any): void {
    this.schema = newSchema;
    console.log('✅ تم تحديث بنية قاعدة البيانات');
  }
}

{"table": "tbltemp_Inv_MainInvoice", "description": "جدول رئيسي يحتوي على معلومات الفواتير يخدم مجال sales_and_inventory ويهدف إلى تتبع المبيعات والمخزون. يحتوي على 33 عمود مهم من أصل 34 عمود.", "fields": {"ID": "معرف فريد للسجل", "DocumentName": "نوع المستند - يحدد نوع العملية (مبيعات، مشتريات، مرتجعات)", "RecordID": "معرف فريد للسجل", "TheNumber": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "SupplierName": "اسم أو وصف", "InvoiceID": "معرف الفاتورة", "DetailsID": "معرف فريد للسجل", "TheDate": "التاريخ - يستخدم في التقارير الزمنية وتحليل الاتجاهات", "CurrencyID": "معرف فريد للسجل", "TheMethod": "اسم أو وصف", "EnterTime": "التاريخ - يستخدم في التقارير الزمنية وتحليل الاتجاهات", "ItemID": "معر<PERSON> المنتج", "UnitID": "معرف فريد للسجل", "UnitPrice": "السعر", "Quantity": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "Bonus": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "TotalAmount": "المبلغ الإجمالي - يستخدم في حساب الإجماليات والتقارير المالية", "MainUnitQuantity": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "MainUnitPrice": "السعر", "MainUnitID": "معرف فريد للسجل", "StoreID": "معرف فريد للسجل", "BranchID": "معرف الفرع - يستخدم في مقارنة أداء الفروع", "ClientID": "معرف العميل - أساسي لتحليل العملاء وتقارير المبيعات", "MCAmount": "مب<PERSON><PERSON> مالي", "ExpiryDate": "التاريخ - يستخدم في التقارير الزمنية وتحليل الاتجاهات", "MainUnitBonus": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "ExchangePrice": "السعر", "DistributorID": "معرف فريد للسجل", "DistributorName": "اسم أو وصف", "CostCenterID": "معرف فريد للسجل", "CostCenterName": "اسم أو وصف", "TotalAmountByCurrencyInvetory": "المبلغ الإجمالي - يستخدم في حساب الإجماليات والتقارير المالية", "NewSubItemEntryID": "معر<PERSON> المنتج"}, "use_cases": [{"name": "إجمالي المبيعات لفترة معينة", "description": "حساب مجموع المبيعات خلال فترة زمنية محددة", "example_sql": "SELECT SUM(UnitPrice) as total_sales FROM tbltemp_Inv_MainInvoice WHERE DocumentName LIKE '%مبيعات%' AND TheDate BETWEEN @start_date AND @end_date", "intent_category": "sales_total", "parameters": ["start_date", "end_date"]}, {"name": "أكثر المنتجات مبيعاً", "description": "عرض المنتجات الأكثر مبيعاً حسب الكمية", "example_sql": "SELECT TOP 10 ItemID, SUM(TheNumber) as total_quantity FROM tbltemp_Inv_MainInvoice WHERE DocumentName LIKE '%مبيعات%' GROUP BY ItemID ORDER BY total_quantity DESC", "intent_category": "top_products", "parameters": ["limit"]}, {"name": "أفضل العملاء", "description": "تحديد العملاء الذين حققوا أعلى مبيعات", "example_sql": "SELECT TOP 10 ClientID, SUM(UnitPrice) as total_amount FROM tbltemp_Inv_MainInvoice WHERE DocumentName LIKE '%مبيعات%' GROUP BY ClientID ORDER BY total_amount DESC", "intent_category": "customer_analysis", "parameters": ["limit"]}, {"name": "أداء الفروع", "description": "مقارنة أداء الفروع المختلفة من حيث المبيعات", "example_sql": "SELECT BranchID, SUM(UnitPrice) as branch_sales FROM tbltemp_Inv_MainInvoice WHERE DocumentName LIKE '%مبيعات%' GROUP BY BranchID ORDER BY branch_sales DESC", "intent_category": "branch_performance", "parameters": []}, {"name": "حالة المخزون", "description": "عرض الكميات المتاحة للمنتجات", "example_sql": "SELECT ItemID, SUM(CASE WHEN DocumentName LIKE '%مبيعات%' THEN -TheNumber WHEN DocumentName LIKE '%مشتريات%' THEN TheNumber ELSE 0 END) as current_stock FROM tbltemp_Inv_MainInvoice GROUP BY ItemID", "intent_category": "inventory_status", "parameters": []}], "business_context": {"domain": "sales_and_inventory", "primary_purpose": "تتبع المبيعات والمخزون", "key_metrics": ["المبالغ المالية", "الإجماليات", "الكميات", "المخزون", "<PERSON><PERSON><PERSON> العملاء", "تحليل العملاء"], "common_filters": ["فلتر التاريخ", "فترة زمنية", "فلتر الفرع", "نوع المستند"]}, "sql_patterns": {"aggregations": ["SUM(Amount) - ل<PERSON><PERSON><PERSON><PERSON> إجمالي المبالغ", "AVG(Amount) - لحسا<PERSON> متوسط المبالغ", "SUM(Quantity) - ل<PERSON><PERSON><PERSON><PERSON> إجمالي الكميات", "COUNT(*) - لعد السجلات"], "joins": [], "filters": ["WHERE TheDate BETWEEN @start_date AND @end_date - فلتر التاريخ", "WHERE YEAR(TheDate) = @year - فلتر السنة", "WHERE MONTH(TheDate) = @month - فلتر الشهر", "WHERE DocumentName LIKE '%مبيعات%' - فلتر المبيعات", "WHERE DocumentName LIKE '%مشتريات%' - فلتر المشتريات", "WHERE BranchID = @branch_name - فلتر الفرع"]}}
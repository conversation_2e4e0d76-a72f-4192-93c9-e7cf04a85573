"use client"

import React, { useState, useEffect } from 'react';
import { AIProvider, AIProviderManager } from '@/lib/ai/ai-provider-manager';
import { Brain, Zap, Globe, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

interface AIProviderSelectorProps {
  onProviderSelect: (provider: AIProvider) => void;
  selectedProvider?: AIProvider;
  className?: string;
}

export function AIProviderSelector({ 
  onProviderSelect, 
  selectedProvider,
  className = "" 
}: AIProviderSelectorProps) {
  const [availabilityStatus, setAvailabilityStatus] = useState<{
    [key in AIProvider]: { available: boolean; error?: string; checking: boolean }
  }>({
    openrouter: { available: false, checking: true }
  });

  const aiManager = AIProviderManager.getInstance();
  const providers = aiManager.getAvailableProviders();

  useEffect(() => {
    checkProvidersAvailability();
  }, []);

  const checkProvidersAvailability = async () => {
    // تعيين حالة الفحص لجميع النماذج
    setAvailabilityStatus(prev => ({
      openrouter: { ...prev.openrouter, checking: true }
    }));

    try {
      // فحص جميع النماذج مرة واحدة
      const allStatuses = await aiManager.checkAllProvidersAvailability();

      setAvailabilityStatus({
        openrouter: {
          available: allStatuses.openrouter.available,
          error: allStatuses.openrouter.error,
          checking: false
        }
      });
    } catch (error) {
      console.error('خطأ في فحص النماذج:', error);
      setAvailabilityStatus({
        openrouter: { available: false, error: 'خطأ في الفحص', checking: false }
      });
    }
  };

  const getProviderIcon = (providerId: AIProvider) => {
    switch (providerId) {
      case 'openrouter':
        return <Globe className="w-6 h-6 text-green-600" />;
      default:
        return <Zap className="w-6 h-6 text-gray-600" />;
    }
  };

  const getStatusIcon = (providerId: AIProvider) => {
    const status = availabilityStatus[providerId];
    
    if (status.checking) {
      return <Loader2 className="w-4 h-4 text-gray-400 animate-spin" />;
    }
    
    if (status.available) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    
    return <AlertCircle className="w-4 h-4 text-red-500" />;
  };

  const handleProviderSelect = (providerId: AIProvider) => {
    const status = availabilityStatus[providerId];
    if (status.available && !status.checking) {
      onProviderSelect(providerId);
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">
          الوكيل الذكي الثلاثي الطبقات
        </h3>
        <p className="text-gray-600 text-sm">
          نظام متقدم مع تصنيف النوايا والتحليل الذكي باستخدام OpenRouter
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {providers.map((provider) => {
          const status = availabilityStatus[provider.id];
          const isSelected = selectedProvider === provider.id;
          const isAvailable = status.available && !status.checking;
          
          return (
            <div
              key={provider.id}
              onClick={() => handleProviderSelect(provider.id)}
              className={`
                relative p-6 rounded-lg border-2 cursor-pointer transition-all duration-200
                ${isSelected 
                  ? 'border-blue-500 bg-blue-50 shadow-md' 
                  : isAvailable
                    ? 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                    : 'border-gray-100 bg-gray-50 cursor-not-allowed opacity-60'
                }
              `}
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3 space-x-reverse">
                  {getProviderIcon(provider.id)}
                  <h4 className="font-semibold text-gray-900">
                    {provider.name}
                  </h4>
                </div>
                {getStatusIcon(provider.id)}
              </div>

              {/* Description */}
              <p className="text-gray-600 text-sm mb-4">
                {provider.description}
              </p>

              {/* Models */}
              <div className="mb-4">
                <p className="text-xs font-medium text-gray-500 mb-2">النماذج المتاحة:</p>
                <div className="flex flex-wrap gap-1">
                  {provider.models.map((model) => (
                    <span
                      key={model}
                      className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md"
                    >
                      {model}
                    </span>
                  ))}
                </div>
              </div>

              {/* Status */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 space-x-reverse">
                  {status.checking ? (
                    <span className="text-xs text-gray-500">جاري الفحص...</span>
                  ) : status.available ? (
                    <span className="text-xs text-green-600 font-medium">متاح</span>
                  ) : (
                    <span className="text-xs text-red-600 font-medium">غير متاح</span>
                  )}
                </div>
                
                {isSelected && (
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <CheckCircle className="w-4 h-4 text-blue-600" />
                    <span className="text-xs text-blue-600 font-medium">محدد</span>
                  </div>
                )}
              </div>

              {/* Error Message */}
              {status.error && !status.checking && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex items-start space-x-2 space-x-reverse">
                    <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-xs font-medium text-red-800 mb-1">خطأ في التكوين:</p>
                      <p className="text-xs text-red-600">{status.error}</p>
                      {status.error.includes('API_KEY') && (
                        <p className="text-xs text-red-500 mt-1">
                          تأكد من إضافة المفتاح في ملف .env.local
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Selected Indicator */}
              {isSelected && (
                <div className="absolute top-2 right-2">
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4 space-x-reverse">
        <button
          onClick={checkProvidersAvailability}
          className="text-sm text-blue-600 hover:text-blue-800 underline"
        >
          إعادة فحص التوفر
        </button>
        <button
          onClick={() => window.location.reload()}
          className="text-sm text-gray-500 hover:text-gray-700 underline"
        >
          إعادة تحميل الصفحة
        </button>
      </div>

      {/* Help Text */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">مميزات النظام الجديد:</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• تصنيف ذكي للنوايا مع 10+ أنواع استعلامات</li>
          <li>• معالجة المرادفات والأنماط العربية</li>
          <li>• قوالب SQL محسنة وآمنة</li>
          <li>• نظام قواعد وحماية متقدم</li>
          <li>• تحليلات ذكية للنتائج</li>
        </ul>
      </div>
    </div>
  );
}

{"table": "tbltemp_ItemsMain", "description": "جدول يحتوي على تفاصيل العناصر والمنتجات في الفواتير يخدم مجال sales_and_inventory ويهدف إلى تتبع المبيعات والمخزون. يحتوي على 63 عمود مهم من أصل 69 عمود.", "fields": {"ID": "معرف فريد للسجل", "ParentID": "معرف فريد للسجل", "RowVersion": "التاريخ - يستخدم في التقارير الزمنية وتحليل الاتجاهات", "DocumentID": "معرف الفاتورة - يحدد نوع العملية (مبيعات، مشتريات، مرتجعات)", "RecordNumber": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "RecordID": "معرف فريد للسجل", "TheDate": "التاريخ - يستخدم في التقارير الزمنية وتحليل الاتجاهات", "ClientID": "معرف العميل - أساسي لتحليل العملاء وتقارير المبيعات", "DistributorID": "معرف فريد للسجل", "CurrencyID": "معرف فريد للسجل", "TheMethodID": "معرف فريد للسجل", "Discount": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "UserID": "معرف فريد للسجل", "BranchID": "معرف الفرع - يستخدم في مقارنة أداء الفروع", "DocumentName": "نوع المستند - يحدد نوع العملية (مبيعات، مشتريات، مرتجعات)", "TheNumber": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "ClientName": "اسم العميل - أساسي لتحليل العملاء وتقارير المبيعات", "DistributorName": "اسم أو وصف", "CurrencyName": "اسم أو وصف", "TheMethod": "اسم أو وصف", "UserName": "اسم أو وصف", "BranchName": "اسم الفرع - يستخدم في مقارنة أداء الفروع", "CategoryID": "معرف فريد للسجل", "FatherNumber": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "CategoryName": "اسم أو وصف", "CategoryNumber": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "ItemID": "معر<PERSON> المنتج", "UnitID": "معرف فريد للسجل", "ItemNumber": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "ItemName": "اسم المنتج", "ItemTypeID": "معر<PERSON> المنتج", "ItemType": "عمود من نوع varchar", "ISActive": "حالة السجل", "ISExpiry": "حالة السجل", "UnitName": "اسم أو وصف", "AccountFatherNumber": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "AccountName": "اسم أو وصف", "AccountNumber": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "CostCenterID": "معرف فريد للسجل", "CostCenterName": "اسم أو وصف", "CostCenterNumber": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "Barcode": "معرف فريد للسجل", "PackageQuantity": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "BarcodeID": "معرف فريد للسجل", "SerialNumber": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "UnitPrice": "السعر", "ItemDiscount": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "McItemDiscountCurrencyMain": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "McItemDiscount": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "Quantity": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "Bonus": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "ExpiryDate": "التاريخ - يستخدم في التقارير الزمنية وتحليل الاتجاهات", "Amount": "مب<PERSON><PERSON> مالي", "MCAmount": "مب<PERSON><PERSON> مالي", "MCAmountCurrencyMain": "مب<PERSON><PERSON> مالي", "AccountID": "معرف فريد للسجل", "StoreID": "معرف فريد للسجل", "StoreName": "اسم أو وصف", "PackageUnitID": "معرف فريد للسجل", "PackageUnitName": "اسم أو وصف", "NextParentID": "معرف فريد للسجل", "ExchangePrice": "السعر", "ExchangePriceCurrencyInvetory": "السعر"}, "use_cases": [{"name": "إجمالي المبيعات لفترة معينة", "description": "حساب مجموع المبيعات خلال فترة زمنية محددة", "example_sql": "SELECT SUM(UnitPrice) as total_sales FROM tbltemp_ItemsMain WHERE DocumentID LIKE '%مبيعات%' AND RowVersion BETWEEN @start_date AND @end_date", "intent_category": "sales_total", "parameters": ["start_date", "end_date"]}, {"name": "أكثر المنتجات مبيعاً", "description": "عرض المنتجات الأكثر مبيعاً حسب الكمية", "example_sql": "SELECT TOP 10 ItemID, SUM(RecordNumber) as total_quantity FROM tbltemp_ItemsMain WHERE DocumentID LIKE '%مبيعات%' GROUP BY ItemID ORDER BY total_quantity DESC", "intent_category": "top_products", "parameters": ["limit"]}, {"name": "أفضل العملاء", "description": "تحديد العملاء الذين حققوا أعلى مبيعات", "example_sql": "SELECT TOP 10 ClientID, SUM(UnitPrice) as total_amount FROM tbltemp_ItemsMain WHERE DocumentID LIKE '%مبيعات%' GROUP BY ClientID ORDER BY total_amount DESC", "intent_category": "customer_analysis", "parameters": ["limit"]}, {"name": "أداء الفروع", "description": "مقارنة أداء الفروع المختلفة من حيث المبيعات", "example_sql": "SELECT BranchID, SUM(UnitPrice) as branch_sales FROM tbltemp_ItemsMain WHERE DocumentID LIKE '%مبيعات%' GROUP BY BranchID ORDER BY branch_sales DESC", "intent_category": "branch_performance", "parameters": []}, {"name": "حالة المخزون", "description": "عرض الكميات المتاحة للمنتجات", "example_sql": "SELECT ItemID, SUM(CASE WHEN DocumentID LIKE '%مبيعات%' THEN -RecordNumber WHEN DocumentID LIKE '%مشتريات%' THEN RecordNumber ELSE 0 END) as current_stock FROM tbltemp_ItemsMain GROUP BY ItemID", "intent_category": "inventory_status", "parameters": []}], "business_context": {"domain": "sales_and_inventory", "primary_purpose": "تتبع المبيعات والمخزون", "key_metrics": ["المبالغ المالية", "الإجماليات", "الكميات", "المخزون", "<PERSON><PERSON><PERSON> العملاء", "تحليل العملاء"], "common_filters": ["فلتر التاريخ", "فترة زمنية", "فلتر الفرع", "نوع المستند"]}, "sql_patterns": {"aggregations": ["SUM(Amount) - ل<PERSON><PERSON><PERSON><PERSON> إجمالي المبالغ", "AVG(Amount) - لحسا<PERSON> متوسط المبالغ", "SUM(Quantity) - ل<PERSON><PERSON><PERSON><PERSON> إجمالي الكميات", "COUNT(*) - لعد السجلات"], "joins": [], "filters": ["WHERE TheDate BETWEEN @start_date AND @end_date - فلتر التاريخ", "WHERE YEAR(TheDate) = @year - فلتر السنة", "WHERE MONTH(TheDate) = @month - فلتر الشهر", "WHERE DocumentName LIKE '%مبيعات%' - فلتر المبيعات", "WHERE DocumentName LIKE '%مشتريات%' - فلتر المشتريات", "WHERE BranchID = @branch_name - فلتر الفرع"]}}
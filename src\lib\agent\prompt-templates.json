{"sql_templates": {"sales_total": {"name": "إجمالي المبيعات", "description": "حساب إجمالي المبيعات لفترة معينة", "base_query": "SELECT SUM(Amount) as total_sales FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مبيعات%'", "filters": {"date_range": "AND TheDate BETWEEN '{start_date}' AND '{end_date}'", "branch": "AND BranchName = '{branch_name}'", "product": "AND ItemName LIKE '%{product_name}%'", "customer": "AND ClientName LIKE '%{customer_name}%'"}, "group_by_options": {"daily": "GROUP BY DATE(TheDate)", "monthly": "GROUP BY YEAR(TheDate), MONTH(TheDate)", "yearly": "GROUP BY YEAR(TheDate)", "branch": "GROUP BY BranchName"}, "order_by": "ORDER BY total_sales DESC"}, "top_products": {"name": "أكثر المنتجات مبيعاً", "description": "عرض المنتجات الأكثر مبيعاً حسب الكمية أو القيمة", "base_query": "SELECT ItemName, SUM(Quantity) as total_quantity, SUM(Amount) as total_amount FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مبيعات%'", "filters": {"date_range": "AND TheDate BETWEEN '{start_date}' AND '{end_date}'", "branch": "AND BranchName = '{branch_name}'", "category": "AND ItemCategory LIKE '%{category}%'"}, "group_by": "GROUP BY ItemName, ItemNumber", "order_by": "ORDER BY total_quantity DESC", "limit": "TOP {limit}"}, "customer_analysis": {"name": "تحليل العملاء", "description": "تحليل أداء العملاء وقيمة مشترياتهم", "base_query": "SELECT ClientName, COUNT(*) as transaction_count, SUM(Amount) as total_spent, AVG(Amount) as avg_transaction FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مبيعات%'", "filters": {"date_range": "AND TheDate BETWEEN '{start_date}' AND '{end_date}'", "branch": "AND BranchName = '{branch_name}'", "min_amount": "AND Amount >= {min_amount}"}, "group_by": "GROUP BY ClientName, ClientID", "having": "HAVING total_spent > {min_total}", "order_by": "ORDER BY total_spent DESC", "limit": "TOP {limit}"}, "inventory_status": {"name": "حالة المخزون", "description": "عرض حالة المخزون الحالية للأصناف", "base_query": "SELECT ItemName, StoreName, SUM(CASE WHEN DocumentName LIKE '%مبيعات%' THEN -Quantity WHEN DocumentName LIKE '%مشتريات%' THEN Quantity ELSE 0 END) as current_stock FROM tbltemp_ItemsMain", "filters": {"product": "WHERE ItemName LIKE '%{product_name}%'", "store": "AND StoreName = '{store_name}'", "branch": "AND BranchName = '{branch_name}'"}, "group_by": "GROUP BY ItemName, ItemNumber, StoreName", "having": "HAVING current_stock {operator} {threshold}", "order_by": "ORDER BY current_stock ASC"}, "purchase_analysis": {"name": "تحليل المشتريات", "description": "تحليل بيانات المشتريات والموردين", "base_query": "SELECT DistributorName, SUM(Amount) as total_purchases, COUNT(*) as purchase_count, AVG(Amount) as avg_purchase FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مشتريات%'", "filters": {"date_range": "AND TheDate BETWEEN '{start_date}' AND '{end_date}'", "supplier": "AND DistributorName LIKE '%{supplier_name}%'", "branch": "AND BranchName = '{branch_name}'"}, "group_by": "GROUP BY DistributorName, DistributorID", "order_by": "ORDER BY total_purchases DESC", "limit": "LIMIT {limit}"}, "financial_reports": {"name": "التقارير المالية", "description": "تقارير الأرباح والخسائر والتدفق النقدي", "base_query": "SELECT SUM(CASE WHEN DocumentName LIKE '%مبيعات%' THEN Amount ELSE 0 END) as revenue, SUM(CASE WHEN DocumentName LIKE '%مشتريات%' THEN Amount ELSE 0 END) as expenses, SUM(CASE WHEN DocumentName LIKE '%مبيعات%' THEN Amount ELSE -Amount END) as profit FROM tbltemp_ItemsMain", "filters": {"date_range": "WHERE TheDate BETWEEN '{start_date}' AND '{end_date}'", "branch": "AND BranchName = '{branch_name}'", "cost_center": "AND CostCenterName = '{cost_center}'"}, "group_by_options": {"monthly": "GROUP BY YEAR(TheDate), MONTH(TheDate)", "quarterly": "GROUP BY YEAR(TheDate), QUARTER(TheDate)", "yearly": "GROUP BY YEAR(TheDate)"}}, "branch_performance": {"name": "أداء الفروع", "description": "مقارنة أداء الفروع المختلفة", "base_query": "SELECT BranchName, SUM(CASE WHEN DocumentName LIKE '%مبيعات%' THEN Amount ELSE 0 END) as sales, COUNT(DISTINCT ClientID) as unique_customers, COUNT(*) as transaction_count FROM tbltemp_ItemsMain", "filters": {"date_range": "WHERE TheDate BETWEEN '{start_date}' AND '{end_date}'", "branch": "AND BranchName IN ({branch_list})"}, "group_by": "GROUP BY BranchName, BranchID", "order_by": "ORDER BY sales DESC"}, "time_analysis": {"name": "التحليل الزمني", "description": "تحليل الاتجاهات عبر الوقت", "base_query": "SELECT {time_dimension}, SUM(Amount) as total_amount, COUNT(*) as transaction_count FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مبيعات%'", "time_dimensions": {"daily": "DATE(TheDate) as date", "weekly": "YEARWEEK(TheDate) as week", "monthly": "CONCAT(YEAR(TheDate), '-', LPAD(MONTH(TheDate), 2, '0')) as month", "quarterly": "CONCAT(YEAR(TheDate), '-Q', QUARTER(TheDate)) as quarter", "yearly": "YEAR(TheDate) as year"}, "filters": {"date_range": "AND TheDate BETWEEN '{start_date}' AND '{end_date}'", "branch": "AND BranchName = '{branch_name}'"}, "group_by": "GROUP BY {time_dimension}", "order_by": "ORDER BY {time_dimension}"}, "product_movement": {"name": "حركة الأصناف", "description": "تتبع حركة الأصناف في المخزون", "base_query": "SELECT TheDate, DocumentName, ItemName, Quantity, Amount, StoreName, UserName FROM tbltemp_ItemsMain", "filters": {"product": "WHERE ItemName LIKE '%{product_name}%'", "date_range": "AND TheDate BETWEEN '{start_date}' AND '{end_date}'", "store": "AND StoreName = '{store_name}'", "movement_type": "AND DocumentName LIKE '%{movement_type}%'"}, "order_by": "ORDER BY TheDate DESC, ItemName"}, "returns_analysis": {"name": "تحليل المرتجعات", "description": "تحليل المرتجعات والإرجاعات", "base_query": "SELECT ItemName, SUM(Quantity) as returned_quantity, SUM(Amount) as returned_amount, COUNT(*) as return_count FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مرتجع%'", "filters": {"date_range": "AND TheDate BETWEEN '{start_date}' AND '{end_date}'", "product": "AND ItemName LIKE '%{product_name}%'", "branch": "AND BranchName = '{branch_name}'"}, "group_by": "GROUP BY ItemName, ItemNumber", "order_by": "ORDER BY returned_quantity DESC"}}, "prompt_templates": {"system_prompt": "أنت خبير في قواعد البيانات وتحليل البيانات التجارية. مهمتك هي تحويل الأسئلة باللغة العربية إلى استعلامات SQL دقيقة وفعالة.\n\nقاعدة البيانات تحتوي على:\n- جدول tbltemp_ItemsMain: يحتوي على تفاصيل جميع المعاملات (مبيعات، مشتريات، مرتجعات)\n- جدول tbltemp_Inv_MainInvoice: يحتوي على رؤوس الفواتير\n\nالأعمدة المهمة:\n- ItemName: اسم المنتج\n- Quantity: الكمية\n- Amount: المبلغ\n- TheDate: التاريخ\n- ClientName: اسم العميل\n- BranchName: اسم الفرع\n- StoreName: اسم المخزن\n- DocumentName: نوع المستند\n- DistributorName: اسم المورد\n\nيجب أن تكون الاستعلامات:\n1. دقيقة ومحسنة للأداء\n2. تتعامل مع البيانات العربية بشكل صحيح\n3. تتضمن التصفية والتجميع المناسب\n4. تعيد نتائج مفيدة وقابلة للفهم", "user_prompt_template": "السؤال: {user_question}\n\nالسياق:\n- النية المكتشفة: {intent}\n- الكيانات المستخرجة: {entities}\n- الأنماط المكتشفة: {patterns}\n\nمعلومات الجداول المتاحة:\n{table_info}\n\nاكتب استعلام SQL يجيب على السؤال بدقة. يجب أن يكون الاستعلام:\n1. محسن للأداء\n2. يتعامل مع البيانات العربية\n3. يتضمن التصفية المناسبة\n4. يعيد النتائج بتنسيق واضح\n\nأرجع الإجابة بصيغة JSON:\n{\n  \"query\": \"استعلام SQL\",\n  \"explanation\": \"شرح الاستعلام\",\n  \"confidence\": 0.95,\n  \"estimated_rows\": 100\n}", "context_enhancement": "لتحسين دقة الاستعلام، خذ في الاعتبار:\n\n1. التواريخ:\n   - 'اليوم' = DATE(NOW())\n   - 'هذا الشهر' = MONTH(NOW()) AND YEAR(NOW())\n   - 'السنة الحالية' = YEAR(NOW())\n\n2. أنواع المستندات:\n   - المبيعات: DocumentName LIKE '%مبيعات%' OR DocumentName LIKE '%بيع%'\n   - المشتريات: DocumentName LIKE '%مشتريات%' OR DocumentName LIKE '%شراء%'\n   - المرتجعات: DocumentName LIKE '%مرتجع%' OR DocumentName LIKE '%إرجاع%'\n\n3. التجميع والترتيب:\n   - استخدم GROUP BY للتجميع المناسب\n   - استخدم ORDER BY للترتيب المنطقي\n   - استخدم LIMIT للحد من النتائج عند الحاجة\n\n4. الأداء:\n   - استخدم الفهارس المناسبة\n   - تجنب SELECT * عند عدم الحاجة\n   - استخدم WHERE بدلاً من HAVING عند الإمكان", "error_handling": "في حالة عدم وضوح السؤال:\n1. اطلب توضيحات محددة\n2. اقترح بدائل ممكنة\n3. اعرض أمثلة للأسئلة المشابهة\n4. تأكد من صحة أسماء الأعمدة والجداول"}}
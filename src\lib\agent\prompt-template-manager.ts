import promptTemplatesData from './prompt-templates.json';
import { ClassificationResult } from './intent-classifier';
import { ProcessedQuery } from './synonym-processor';
import { DatabaseSchema } from './schema-generator';
import { AgentProfile } from './agent-profile-generator';

export interface SQLTemplate {
  name: string;
  description: string;
  base_query: string;
  filters?: { [key: string]: string };
  group_by?: string;
  group_by_options?: { [key: string]: string };
  having?: string;
  order_by?: string;
  limit?: string;
  time_dimensions?: { [key: string]: string };
}

export interface GeneratedPrompt {
  systemPrompt: string;
  userPrompt: string;
  context: string;
  templateUsed: string;
  parameters: { [key: string]: any };
}

export interface SQLQueryResult {
  query: string;
  explanation: string;
  confidence: number;
  estimated_rows?: number;
  template_used: string;
  parameters_applied: { [key: string]: any };
}

/**
 * مدير قوالب البرومبت - ينشئ برومبتات محسنة لتوليد استعلامات SQL
 */
export class PromptTemplateManager {
  private sqlTemplates: { [key: string]: SQLTemplate };
  private promptTemplates: { [key: string]: string };

  constructor() {
    this.sqlTemplates = promptTemplatesData.sql_templates;
    this.promptTemplates = promptTemplatesData.prompt_templates;
  }

  /**
   * إنشاء برومبت محسن بناءً على النية والسياق
   */
  generatePrompt(
    classificationResult: ClassificationResult,
    processedQuery: ProcessedQuery,
    tableInfo: string,
    schema?: DatabaseSchema,
    agentProfile?: AgentProfile
  ): GeneratedPrompt {
    const intentId = classificationResult.intent.id;
    const template = this.sqlTemplates[intentId];
    
    if (!template) {
      throw new Error(`لم يتم العثور على قالب للنية: ${intentId}`);
    }

    // استخراج المعاملات من الكيانات والأنماط
    const parameters = this.extractParameters(classificationResult, processedQuery);
    
    // بناء البرومبت المحسن
    const systemPrompt = this.buildEnhancedSystemPrompt(schema, agentProfile);
    const userPrompt = this.buildUserPrompt(
      classificationResult,
      processedQuery,
      tableInfo,
      parameters,
      agentProfile
    );
    const context = this.buildContext(template, parameters);

    return {
      systemPrompt,
      userPrompt,
      context,
      templateUsed: intentId,
      parameters
    };
  }

  /**
   * بناء استعلام SQL من القالب والمعاملات
   */
  buildSQLFromTemplate(
    templateId: string,
    parameters: { [key: string]: any }
  ): SQLQueryResult {
    const template = this.sqlTemplates[templateId];
    if (!template) {
      throw new Error(`قالب غير موجود: ${templateId}`);
    }

    let query = template.base_query;
    const appliedParameters: { [key: string]: any } = {};

    // تطبيق الفلاتر
    if (template.filters) {
      for (const [filterKey, filterTemplate] of Object.entries(template.filters)) {
        if (parameters[filterKey]) {
          const filterClause = this.applyParametersToTemplate(filterTemplate, parameters);
          query += ' ' + filterClause;
          appliedParameters[filterKey] = parameters[filterKey];
        }
      }
    }

    // تطبيق GROUP BY
    if (template.group_by) {
      query += ' ' + template.group_by;
    } else if (template.group_by_options && parameters.group_by_type) {
      const groupByClause = template.group_by_options[parameters.group_by_type];
      if (groupByClause) {
        query += ' ' + groupByClause;
        appliedParameters.group_by_type = parameters.group_by_type;
      }
    }

    // تطبيق HAVING
    if (template.having && parameters.having_condition) {
      const havingClause = this.applyParametersToTemplate(template.having, parameters);
      query += ' ' + havingClause;
      appliedParameters.having_condition = parameters.having_condition;
    }

    // تطبيق ORDER BY
    if (template.order_by) {
      query += ' ' + template.order_by;
    }

    // تطبيق LIMIT
    if (template.limit && parameters.limit) {
      const limitClause = this.applyParametersToTemplate(template.limit, parameters);
      query += ' ' + limitClause;
      appliedParameters.limit = parameters.limit;
    }

    return {
      query: query.trim(),
      explanation: `استعلام ${template.name}: ${template.description}`,
      confidence: 0.9,
      template_used: templateId,
      parameters_applied: appliedParameters
    };
  }

  /**
   * استخراج المعاملات من نتائج التصنيف والمعالجة
   */
  private extractParameters(
    classificationResult: ClassificationResult,
    processedQuery: ProcessedQuery
  ): { [key: string]: any } {
    const parameters: { [key: string]: any } = {};

    // استخراج التواريخ
    const datePatterns = processedQuery.detectedPatterns.filter(p => 
      p.type.includes('date') || p.type.includes('time')
    );
    
    if (datePatterns.length > 0) {
      const datePattern = datePatterns[0];
      if (datePattern.variables.start_date && datePattern.variables.end_date) {
        parameters.start_date = datePattern.variables.start_date;
        parameters.end_date = datePattern.variables.end_date;
      } else {
        // تحويل التواريخ النسبية
        const dateRange = this.convertRelativeDate(datePattern.match);
        if (dateRange) {
          parameters.start_date = dateRange.start;
          parameters.end_date = dateRange.end;
        }
      }
    }

    // استخراج الكيانات
    for (const entity of classificationResult.entities) {
      switch (entity.type) {
        case 'branch':
          parameters.branch_name = entity.value;
          break;
        case 'product':
          parameters.product_name = entity.value;
          break;
        case 'customer':
          parameters.customer_name = entity.value;
          break;
        case 'supplier':
          parameters.supplier_name = entity.value;
          break;
      }
    }

    // استخراج الأرقام
    if (processedQuery.extractedNumbers.length > 0) {
      const firstNumber = processedQuery.extractedNumbers[0];
      parameters.limit = firstNumber.numeric;
    }

    // تعيين قيم افتراضية
    if (!parameters.limit) {
      parameters.limit = 10; // حد افتراضي
    }

    return parameters;
  }

  /**
   * بناء System Prompt محسن مع Schema وAgent Profile
   */
  private buildEnhancedSystemPrompt(schema?: DatabaseSchema, agentProfile?: AgentProfile): string {
    let systemPrompt = this.promptTemplates.system_prompt;

    if (agentProfile) {
      systemPrompt += `\n\nمعلومات الجدول المحسنة:\n`;
      systemPrompt += `جدول ${agentProfile.table}: ${agentProfile.description}\n\n`;

      systemPrompt += `الحقول المهمة:\n`;
      Object.entries(agentProfile.fields).forEach(([field, description]) => {
        systemPrompt += `- ${field}: ${description}\n`;
      });

      systemPrompt += `\nحالات الاستخدام الشائعة:\n`;
      agentProfile.use_cases.forEach((useCase, index) => {
        systemPrompt += `${index + 1}. ${useCase.name}: ${useCase.description}\n`;
      });

      systemPrompt += `\nأنماط SQL المفيدة:\n`;
      agentProfile.sql_patterns.aggregations.forEach(pattern => {
        systemPrompt += `- ${pattern}\n`;
      });
      agentProfile.sql_patterns.filters.forEach(pattern => {
        systemPrompt += `- ${pattern}\n`;
      });
    }

    return systemPrompt;
  }

  /**
   * بناء برومبت المستخدم
   */
  private buildUserPrompt(
    classificationResult: ClassificationResult,
    processedQuery: ProcessedQuery,
    tableInfo: string,
    parameters: { [key: string]: any },
    agentProfile?: AgentProfile
  ): string {
    const template = this.promptTemplates.user_prompt_template;

    // استخدام معلومات Agent Profile إذا كانت متاحة
    let enhancedTableInfo = tableInfo;
    if (agentProfile) {
      enhancedTableInfo = this.generateEnhancedTableInfo(agentProfile);
    }

    return template
      .replace('{user_question}', classificationResult.originalQuery)
      .replace('{intent}', classificationResult.intent.name)
      .replace('{entities}', JSON.stringify(classificationResult.entities, null, 2))
      .replace('{patterns}', JSON.stringify(processedQuery.detectedPatterns, null, 2))
      .replace('{table_info}', enhancedTableInfo);
  }

  /**
   * توليد معلومات جدول محسنة من Agent Profile
   */
  private generateEnhancedTableInfo(agentProfile: AgentProfile): string {
    let info = `جدول ${agentProfile.table}:\n`;
    info += `${agentProfile.description}\n\n`;

    info += 'الحقول المهمة:\n';
    Object.entries(agentProfile.fields).forEach(([field, description]) => {
      info += `- ${field}: ${description}\n`;
    });

    info += '\nأمثلة SQL مفيدة:\n';
    agentProfile.use_cases.slice(0, 3).forEach((useCase, index) => {
      info += `${index + 1}. ${useCase.name}:\n`;
      info += `   ${useCase.example_sql}\n\n`;
    });

    return info;
  }

  /**
   * بناء السياق الإضافي
   */
  private buildContext(template: SQLTemplate, parameters: { [key: string]: any }): string {
    let context = this.promptTemplates.context_enhancement;
    
    // إضافة معلومات القالب
    context += `\n\nقالب الاستعلام المقترح:\n${template.description}\n`;
    context += `الاستعلام الأساسي: ${template.base_query}\n`;
    
    // إضافة المعاملات المتاحة
    if (Object.keys(parameters).length > 0) {
      context += `\nالمعاملات المستخرجة:\n${JSON.stringify(parameters, null, 2)}`;
    }
    
    return context;
  }

  /**
   * تطبيق المعاملات على قالب النص
   */
  private applyParametersToTemplate(template: string, parameters: { [key: string]: any }): string {
    let result = template;
    
    for (const [key, value] of Object.entries(parameters)) {
      const placeholder = `{${key}}`;
      result = result.replace(new RegExp(placeholder, 'g'), String(value));
    }
    
    return result;
  }

  /**
   * تحويل التواريخ النسبية إلى تواريخ فعلية
   */
  private convertRelativeDate(dateText: string): { start: string; end: string } | null {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (dateText.toLowerCase()) {
      case 'اليوم':
        return {
          start: today.toISOString().split('T')[0],
          end: today.toISOString().split('T')[0]
        };
      
      case 'أمس':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        return {
          start: yesterday.toISOString().split('T')[0],
          end: yesterday.toISOString().split('T')[0]
        };
      
      case 'هذا الأسبوع':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        return {
          start: weekStart.toISOString().split('T')[0],
          end: today.toISOString().split('T')[0]
        };
      
      case 'هذا الشهر':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        return {
          start: monthStart.toISOString().split('T')[0],
          end: today.toISOString().split('T')[0]
        };
      
      case 'هذه السنة':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        return {
          start: yearStart.toISOString().split('T')[0],
          end: today.toISOString().split('T')[0]
        };
      
      default:
        return null;
    }
  }

  /**
   * الحصول على قالب بالمعرف
   */
  getTemplate(templateId: string): SQLTemplate | undefined {
    return this.sqlTemplates[templateId];
  }

  /**
   * الحصول على جميع القوالب المتاحة
   */
  getAvailableTemplates(): { [key: string]: SQLTemplate } {
    return this.sqlTemplates;
  }

  /**
   * إضافة قالب جديد
   */
  addTemplate(templateId: string, template: SQLTemplate): void {
    this.sqlTemplates[templateId] = template;
  }

  /**
   * تحديث قالب موجود
   */
  updateTemplate(templateId: string, updates: Partial<SQLTemplate>): boolean {
    if (this.sqlTemplates[templateId]) {
      this.sqlTemplates[templateId] = { ...this.sqlTemplates[templateId], ...updates };
      return true;
    }
    return false;
  }
}

{"tables": [{"name": "tbltemp_Inv_MainInvoice", "description": "جدول رئيسي يحتوي على معلومات الفواتير", "columns": [{"name": "ID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "description": "نوع المستند", "isImportant": true, "category": "name"}, {"name": "RecordID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "TheNumber", "type": "bigint", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "SupplierName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "InvoiceID", "type": "bigint", "description": "معرف الفاتورة", "isImportant": true, "category": "id"}, {"name": "DetailsID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "TheDate", "type": "datetime", "description": "التاريخ", "isImportant": true, "category": "date"}, {"name": "CurrencyID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "EnterTime", "type": "datetime", "description": "التاريخ", "isImportant": true, "category": "date"}, {"name": "ItemID", "type": "bigint", "description": "معر<PERSON> المنتج", "isImportant": true, "category": "id"}, {"name": "UnitID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "UnitPrice", "type": "numeric", "description": "السعر", "isImportant": true, "category": "amount"}, {"name": "Quantity", "type": "numeric", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "Bonus", "type": "numeric", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "TotalAmount", "type": "numeric", "description": "المبلغ الإجمالي", "isImportant": true, "category": "amount"}, {"name": "MainUnitQuantity", "type": "numeric", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "MainUnitPrice", "type": "numeric", "description": "السعر", "isImportant": true, "category": "amount"}, {"name": "MainUnitID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "StoreID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "BranchID", "type": "bigint", "description": "معرف الفرع", "isImportant": true, "category": "id"}, {"name": "ExchangeFactor", "type": "numeric", "description": "عمود من نوع numeric", "isImportant": false, "category": "other"}, {"name": "ClientID", "type": "bigint", "description": "معر<PERSON> العميل", "isImportant": true, "category": "id"}, {"name": "MCAmount", "type": "decimal", "description": "مب<PERSON><PERSON> مالي", "isImportant": true, "category": "amount"}, {"name": "ExpiryDate", "type": "date", "description": "التاريخ", "isImportant": true, "category": "date"}, {"name": "MainUnitBonus", "type": "numeric", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "ExchangePrice", "type": "numeric", "description": "السعر", "isImportant": true, "category": "amount"}, {"name": "DistributorID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "CostCenterID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "TotalAmountByCurrencyInvetory", "type": "numeric", "description": "المبلغ الإجمالي", "isImportant": true, "category": "amount"}, {"name": "NewSubItemEntryID", "type": "bigint", "description": "معر<PERSON> المنتج", "isImportant": true, "category": "id"}], "primaryKey": "ID"}, {"name": "tbltemp_ItemsMain", "description": "جدول يحتوي على تفاصيل العناصر والمنتجات في الفواتير", "columns": [{"name": "ID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "ParentID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "RowVersion", "type": "timestamp", "description": "التاريخ", "isImportant": true, "category": "date"}, {"name": "DocumentID", "type": "bigint", "description": "معرف الفاتورة", "isImportant": true, "category": "id"}, {"name": "RecordNumber", "type": "bigint", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "RecordID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "TheDate", "type": "datetime", "description": "التاريخ", "isImportant": true, "category": "date"}, {"name": "ClientID", "type": "bigint", "description": "معر<PERSON> العميل", "isImportant": true, "category": "id"}, {"name": "DistributorID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "CurrencyID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "TheMethodID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "Discount", "type": "numeric", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "Notes", "type": "<PERSON><PERSON><PERSON>", "description": "عمود من نوع varchar", "isImportant": false, "category": "other"}, {"name": "UserID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "BranchID", "type": "bigint", "description": "معرف الفرع", "isImportant": true, "category": "id"}, {"name": "TheYear", "type": "int", "description": "عمود من نوع int", "isImportant": false, "category": "other"}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "description": "نوع المستند", "isImportant": true, "category": "name"}, {"name": "TheNumber", "type": "bigint", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "ClientName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم العميل", "isImportant": true, "category": "name"}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "UserName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "BranchName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم الفرع", "isImportant": true, "category": "name"}, {"name": "CategoryID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bigint", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "CategoryName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "CategoryNumber", "type": "bigint", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "ItemID", "type": "bigint", "description": "معر<PERSON> المنتج", "isImportant": true, "category": "id"}, {"name": "UnitID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "ItemNumber", "type": "bigint", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "ItemName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم المنتج", "isImportant": true, "category": "name"}, {"name": "ItemTypeID", "type": "bigint", "description": "معر<PERSON> المنتج", "isImportant": true, "category": "id"}, {"name": "ItemType", "type": "<PERSON><PERSON><PERSON>", "description": "عمود من نوع varchar", "isImportant": true, "category": "other"}, {"name": "ReorderPoint", "type": "numeric", "description": "عمود من نوع numeric", "isImportant": false, "category": "other"}, {"name": "ISActive", "type": "bit", "description": "حالة السجل", "isImportant": true, "category": "status"}, {"name": "ISExpiry", "type": "bit", "description": "حالة السجل", "isImportant": true, "category": "status"}, {"name": "ExpiryPoint", "type": "bigint", "description": "عمود من نوع bigint", "isImportant": false, "category": "other"}, {"name": "UnitName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "AccountFatherNumber", "type": "bigint", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "Account<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "AccountNumber", "type": "bigint", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "CostCenterID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "CostCenterNumber", "type": "bigint", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "Barcode", "type": "<PERSON><PERSON><PERSON>", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "UnitRank", "type": "bigint", "description": "عمود من نوع bigint", "isImportant": false, "category": "other"}, {"name": "ExchangeFactor", "type": "numeric", "description": "عمود من نوع numeric", "isImportant": false, "category": "other"}, {"name": "PackageQuantity", "type": "numeric", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "BarcodeID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "SerialNumber", "type": "<PERSON><PERSON><PERSON>", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "UnitPrice", "type": "numeric", "description": "السعر", "isImportant": true, "category": "amount"}, {"name": "ItemD<PERSON>unt", "type": "numeric", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "McItemDiscountCurrencyMain", "type": "numeric", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "Mc<PERSON>temDiscount", "type": "numeric", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "Quantity", "type": "numeric", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "Bonus", "type": "numeric", "description": "الكمية", "isImportant": true, "category": "quantity"}, {"name": "ExpiryDate", "type": "date", "description": "التاريخ", "isImportant": true, "category": "date"}, {"name": "Amount", "type": "numeric", "description": "مب<PERSON><PERSON> مالي", "isImportant": true, "category": "amount"}, {"name": "MCAmount", "type": "numeric", "description": "مب<PERSON><PERSON> مالي", "isImportant": true, "category": "amount"}, {"name": "MCAmountCurrencyMain", "type": "decimal", "description": "مب<PERSON><PERSON> مالي", "isImportant": true, "category": "amount"}, {"name": "AccountID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "StoreID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "StoreName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "PackageUnitID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "PackageUnitName", "type": "<PERSON><PERSON><PERSON>", "description": "اسم أو وصف", "isImportant": true, "category": "name"}, {"name": "NextParentID", "type": "bigint", "description": "معرف فريد للسجل", "isImportant": true, "category": "id"}, {"name": "ExchangePrice", "type": "numeric", "description": "السعر", "isImportant": true, "category": "amount"}, {"name": "ExchangePriceCurrencyInvetory", "type": "decimal", "description": "السعر", "isImportant": true, "category": "amount"}], "primaryKey": "ID"}], "version": "1.0.0", "generatedAt": "2025-07-22T04:08:00.235Z", "metadata": {"totalTables": 2, "totalColumns": 103, "importantColumns": 96}}
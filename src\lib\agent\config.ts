/**
 * إعدادات النظام الثلاثي الطبقات
 */

export interface SystemConfig {
  // إعدادات الوكيل الذكي
  agent: {
    enableCaching: boolean;
    maxRetries: number;
    timeoutMs: number;
    debugMode: boolean;
  };
  
  // إعدادات تصنيف النوايا
  intentClassification: {
    confidenceThreshold: number;
    maxIntents: number;
    enableLearning: boolean;
  };
  
  // إعدادات معالجة المرادفات
  synonymProcessing: {
    enableExpansion: boolean;
    maxSynonyms: number;
    enablePatternDetection: boolean;
  };
  
  // إعدادات قوالب البرومبت
  promptTemplates: {
    enableOptimization: boolean;
    maxTemplateLength: number;
    enableCustomTemplates: boolean;
  };
  
  // إعدادات نظام القواعد
  rulesEngine: {
    enableSecurity: boolean;
    enableAccessControl: boolean;
    enableAuditLog: boolean;
    maxQueryLength: number;
  };
  
  // إعدادات قاعدة البيانات
  database: {
    enableQueryOptimization: boolean;
    enableCaching: boolean;
    maxResultRows: number;
    queryTimeoutMs: number;
  };
  
  // إعدادات التحليل
  analysis: {
    enableAdvancedAnalysis: boolean;
    enableTrendDetection: boolean;
    enableOutlierDetection: boolean;
  };
}

/**
 * الإعدادات الافتراضية للنظام
 */
export const DEFAULT_CONFIG: SystemConfig = {
  agent: {
    enableCaching: true,
    maxRetries: 3,
    timeoutMs: 30000,
    debugMode: false
  },
  
  intentClassification: {
    confidenceThreshold: 0.7,
    maxIntents: 50,
    enableLearning: false
  },
  
  synonymProcessing: {
    enableExpansion: true,
    maxSynonyms: 100,
    enablePatternDetection: true
  },
  
  promptTemplates: {
    enableOptimization: true,
    maxTemplateLength: 2000,
    enableCustomTemplates: true
  },
  
  rulesEngine: {
    enableSecurity: true,
    enableAccessControl: true,
    enableAuditLog: true,
    maxQueryLength: 2000
  },
  
  database: {
    enableQueryOptimization: true,
    enableCaching: true,
    maxResultRows: 10000,
    queryTimeoutMs: 30000
  },
  
  analysis: {
    enableAdvancedAnalysis: true,
    enableTrendDetection: true,
    enableOutlierDetection: true
  }
};

/**
 * إعدادات التطوير (للاختبار والتطوير)
 */
export const DEVELOPMENT_CONFIG: SystemConfig = {
  ...DEFAULT_CONFIG,
  agent: {
    ...DEFAULT_CONFIG.agent,
    debugMode: true,
    timeoutMs: 60000
  },
  rulesEngine: {
    ...DEFAULT_CONFIG.rulesEngine,
    enableSecurity: false // تعطيل الأمان للاختبار
  }
};

/**
 * إعدادات الإنتاج (للبيئة الحية)
 */
export const PRODUCTION_CONFIG: SystemConfig = {
  ...DEFAULT_CONFIG,
  agent: {
    ...DEFAULT_CONFIG.agent,
    debugMode: false,
    maxRetries: 5
  },
  rulesEngine: {
    ...DEFAULT_CONFIG.rulesEngine,
    enableSecurity: true,
    enableAuditLog: true
  }
};

/**
 * إعدادات الاختبار
 */
export const TEST_CONFIG: SystemConfig = {
  ...DEFAULT_CONFIG,
  agent: {
    ...DEFAULT_CONFIG.agent,
    debugMode: true,
    timeoutMs: 10000,
    maxRetries: 1
  },
  database: {
    ...DEFAULT_CONFIG.database,
    maxResultRows: 100,
    queryTimeoutMs: 5000
  }
};

/**
 * دالة للحصول على الإعدادات حسب البيئة
 */
export function getConfig(environment: 'development' | 'production' | 'test' = 'development'): SystemConfig {
  switch (environment) {
    case 'production':
      return PRODUCTION_CONFIG;
    case 'test':
      return TEST_CONFIG;
    case 'development':
    default:
      return DEVELOPMENT_CONFIG;
  }
}

/**
 * دالة لدمج الإعدادات المخصصة مع الافتراضية
 */
export function mergeConfig(customConfig: Partial<SystemConfig>, baseConfig: SystemConfig = DEFAULT_CONFIG): SystemConfig {
  return {
    agent: { ...baseConfig.agent, ...customConfig.agent },
    intentClassification: { ...baseConfig.intentClassification, ...customConfig.intentClassification },
    synonymProcessing: { ...baseConfig.synonymProcessing, ...customConfig.synonymProcessing },
    promptTemplates: { ...baseConfig.promptTemplates, ...customConfig.promptTemplates },
    rulesEngine: { ...baseConfig.rulesEngine, ...customConfig.rulesEngine },
    database: { ...baseConfig.database, ...customConfig.database },
    analysis: { ...baseConfig.analysis, ...customConfig.analysis }
  };
}

/**
 * دالة للتحقق من صحة الإعدادات
 */
export function validateConfig(config: SystemConfig): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // التحقق من إعدادات الوكيل
  if (config.agent.maxRetries < 1 || config.agent.maxRetries > 10) {
    errors.push('عدد المحاولات يجب أن يكون بين 1 و 10');
  }

  if (config.agent.timeoutMs < 1000 || config.agent.timeoutMs > 300000) {
    errors.push('مهلة الانتظار يجب أن تكون بين 1 ثانية و 5 دقائق');
  }

  // التحقق من إعدادات تصنيف النوايا
  if (config.intentClassification.confidenceThreshold < 0 || config.intentClassification.confidenceThreshold > 1) {
    errors.push('عتبة الثقة يجب أن تكون بين 0 و 1');
  }

  // التحقق من إعدادات قاعدة البيانات
  if (config.database.maxResultRows < 1 || config.database.maxResultRows > 100000) {
    errors.push('عدد النتائج الأقصى يجب أن يكون بين 1 و 100000');
  }

  // التحقق من إعدادات نظام القواعد
  if (config.rulesEngine.maxQueryLength < 100 || config.rulesEngine.maxQueryLength > 10000) {
    errors.push('طول الاستعلام الأقصى يجب أن يكون بين 100 و 10000 حرف');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * دالة لطباعة الإعدادات الحالية
 */
export function printConfig(config: SystemConfig): void {
  console.log('⚙️ إعدادات النظام الحالية:');
  console.log('━'.repeat(50));
  
  console.log('\n🤖 الوكيل الذكي:');
  console.log(`   • التخزين المؤقت: ${config.agent.enableCaching ? 'مفعل' : 'معطل'}`);
  console.log(`   • عدد المحاولات: ${config.agent.maxRetries}`);
  console.log(`   • مهلة الانتظار: ${config.agent.timeoutMs}ms`);
  console.log(`   • وضع التصحيح: ${config.agent.debugMode ? 'مفعل' : 'معطل'}`);
  
  console.log('\n🎯 تصنيف النوايا:');
  console.log(`   • عتبة الثقة: ${config.intentClassification.confidenceThreshold}`);
  console.log(`   • عدد النوايا الأقصى: ${config.intentClassification.maxIntents}`);
  console.log(`   • التعلم التلقائي: ${config.intentClassification.enableLearning ? 'مفعل' : 'معطل'}`);
  
  console.log('\n🔒 نظام القواعد:');
  console.log(`   • الأمان: ${config.rulesEngine.enableSecurity ? 'مفعل' : 'معطل'}`);
  console.log(`   • التحكم في الوصول: ${config.rulesEngine.enableAccessControl ? 'مفعل' : 'معطل'}`);
  console.log(`   • سجل المراجعة: ${config.rulesEngine.enableAuditLog ? 'مفعل' : 'معطل'}`);
  
  console.log('\n🗃️ قاعدة البيانات:');
  console.log(`   • تحسين الاستعلامات: ${config.database.enableQueryOptimization ? 'مفعل' : 'معطل'}`);
  console.log(`   • التخزين المؤقت: ${config.database.enableCaching ? 'مفعل' : 'معطل'}`);
  console.log(`   • عدد النتائج الأقصى: ${config.database.maxResultRows}`);
  
  console.log('\n📊 التحليل:');
  console.log(`   • التحليل المتقدم: ${config.analysis.enableAdvancedAnalysis ? 'مفعل' : 'معطل'}`);
  console.log(`   • كشف الاتجاهات: ${config.analysis.enableTrendDetection ? 'مفعل' : 'معطل'}`);
  console.log(`   • كشف القيم الشاذة: ${config.analysis.enableOutlierDetection ? 'مفعل' : 'معطل'}`);
  
  console.log('━'.repeat(50));
}

/**
 * دالة لحفظ الإعدادات في ملف
 */
export function saveConfigToFile(config: SystemConfig, filename: string = 'system-config.json'): void {
  try {
    const fs = require('fs');
    fs.writeFileSync(filename, JSON.stringify(config, null, 2));
    console.log(`✅ تم حفظ الإعدادات في ${filename}`);
  } catch (error) {
    console.error(`❌ فشل في حفظ الإعدادات: ${error}`);
  }
}

/**
 * دالة لتحميل الإعدادات من ملف
 */
export function loadConfigFromFile(filename: string = 'system-config.json'): SystemConfig | null {
  try {
    const fs = require('fs');
    const configData = fs.readFileSync(filename, 'utf8');
    const config = JSON.parse(configData) as SystemConfig;
    
    const validation = validateConfig(config);
    if (!validation.isValid) {
      console.error('❌ إعدادات غير صحيحة:', validation.errors);
      return null;
    }
    
    console.log(`✅ تم تحميل الإعدادات من ${filename}`);
    return config;
  } catch (error) {
    console.error(`❌ فشل في تحميل الإعدادات: ${error}`);
    return null;
  }
}

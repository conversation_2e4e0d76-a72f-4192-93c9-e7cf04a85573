#!/usr/bin/env node

/**
 * سكريبت اختبار النظام الثلاثي الطبقات
 */

const { createIntelligentAgent, createUserContext, printSystemInfo, SYSTEM_INFO } = require('../src/lib/agent');
const { runEnhancedSystemTests } = require('../src/lib/agent/enhanced-system-test');

async function main() {
  console.log('🚀 بدء اختبار النظام الثلاثي الطبقات...\n');

  try {
    // طباعة معلومات النظام
    printSystemInfo();
    console.log('\n' + '='.repeat(80) + '\n');

    // إنشاء الوكيل الذكي
    console.log('🤖 إنشاء الوكيل الذكي...');
    const agent = createIntelligentAgent({
      debugMode: true,
      enableCaching: true,
      maxRetries: 2,
      timeoutMs: 10000
    });
    console.log('✅ تم إنشاء الوكيل بنجاح\n');

    // إنشاء سياق المستخدم
    console.log('👤 إنشاء سياق المستخدم...');
    const userContext = createUserContext('test_user', 'manager', 'الرياض');
    console.log('✅ تم إنشاء سياق المستخدم بنجاح');
    console.log(`   المستخدم: ${userContext.userId}`);
    console.log(`   الدور: ${userContext.role}`);
    console.log(`   الفرع: ${userContext.branchId}`);
    console.log(`   الصلاحيات: ${userContext.permissions.join(', ')}\n`);

    // معلومات الجداول (محاكاة)
    const tableInfo = `
      جدول tbltemp_ItemsMain:
      - ItemName: اسم المنتج (نص)
      - Quantity: الكمية (رقم)
      - Amount: المبلغ (رقم عشري)
      - TheDate: التاريخ (تاريخ)
      - ClientName: اسم العميل (نص)
      - BranchName: اسم الفرع (نص)
      - DocumentName: نوع المستند (نص)
      - DistributorName: اسم المورد (نص)
      - StoreName: اسم المخزن (نص)
      - UserName: اسم المستخدم (نص)
    `;

    // اختبارات مختلفة
    const testQueries = [
      {
        query: 'إجمالي المبيعات اليوم',
        description: 'استعلام بسيط عن المبيعات اليومية'
      },
      {
        query: 'أكثر 5 منتجات مبيعاً هذا الشهر',
        description: 'استعلام عن أفضل المنتجات مع تحديد العدد'
      },
      {
        query: 'أفضل العملاء في فرع الرياض',
        description: 'تحليل العملاء مع فلتر الفرع'
      },
      {
        query: 'حالة مخزون الهواتف الذكية',
        description: 'استعلام عن المخزون لمنتج محدد'
      },
      {
        query: 'مشتريات من مورد الشركة الذهبية الشهر الماضي',
        description: 'تحليل المشتريات مع فلتر المورد والتاريخ'
      }
    ];

    console.log('🧪 بدء الاختبارات...\n');

    let successCount = 0;
    let totalTests = testQueries.length;

    for (let i = 0; i < testQueries.length; i++) {
      const testCase = testQueries[i];
      console.log(`📝 اختبار ${i + 1}/${totalTests}: ${testCase.description}`);
      console.log(`   الاستعلام: "${testCase.query}"`);

      try {
        const startTime = Date.now();
        const response = await agent.processQuery(
          testCase.query,
          userContext,
          tableInfo
        );
        const endTime = Date.now();

        if (response.success) {
          console.log(`   ✅ نجح الاستعلام`);
          console.log(`   🎯 النية المكتشفة: ${response.metadata.intent}`);
          console.log(`   🎲 مستوى الثقة: ${((response.confidence || 0) * 100).toFixed(1)}%`);
          console.log(`   ⚡ وقت المعالجة: ${endTime - startTime}ms`);
          console.log(`   🔒 القواعد المطبقة: ${response.metadata.rulesApplied.join(', ')}`);
          
          if (response.query) {
            console.log(`   📝 الاستعلام المولد: ${response.query.substring(0, 100)}...`);
          }
          
          if (response.warnings && response.warnings.length > 0) {
            console.log(`   ⚠️ تحذيرات: ${response.warnings.join(', ')}`);
          }

          successCount++;
        } else {
          console.log(`   ❌ فشل الاستعلام: ${response.error}`);
          if (response.warnings && response.warnings.length > 0) {
            console.log(`   ⚠️ تحذيرات: ${response.warnings.join(', ')}`);
          }
        }

      } catch (error) {
        console.log(`   💥 خطأ في الاختبار: ${error.message}`);
      }

      console.log('   ' + '─'.repeat(60));
    }

    // النتائج النهائية
    console.log('\n📊 نتائج الاختبار:');
    console.log(`   ✅ نجح: ${successCount}/${totalTests} (${((successCount/totalTests) * 100).toFixed(1)}%)`);
    console.log(`   ❌ فشل: ${totalTests - successCount}/${totalTests}`);

    // اختبار الأمان
    console.log('\n🔒 اختبار الأمان...');
    const maliciousQueries = [
      "'; DROP TABLE tbltemp_ItemsMain; --",
      "UNION SELECT * FROM users",
      "DELETE FROM tbltemp_ItemsMain"
    ];

    let blockedCount = 0;
    for (const maliciousQuery of maliciousQueries) {
      console.log(`   🧪 اختبار: ${maliciousQuery.substring(0, 30)}...`);
      
      try {
        const response = await agent.processQuery(maliciousQuery, userContext, tableInfo);
        
        if (!response.success) {
          console.log(`   ✅ تم حظر الاستعلام الخبيث`);
          blockedCount++;
        } else {
          console.log(`   ❌ لم يتم حظر الاستعلام الخبيث!`);
        }
      } catch (error) {
        console.log(`   ✅ تم حظر الاستعلام الخبيث (استثناء)`);
        blockedCount++;
      }
    }

    console.log(`   🛡️ تم حظر ${blockedCount}/${maliciousQueries.length} استعلام خبيث`);
    console.log(`   📊 معدل الحماية: ${((blockedCount/maliciousQueries.length) * 100).toFixed(1)}%`);

    // اختبار الأداء
    console.log('\n⚡ اختبار الأداء...');
    const performanceQuery = 'إجمالي المبيعات اليوم';
    const iterations = 5;
    const times = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      await agent.processQuery(performanceQuery, userContext, tableInfo);
      const endTime = Date.now();
      times.push(endTime - startTime);
    }

    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);

    console.log(`   📊 ${iterations} استعلام متتالي`);
    console.log(`   ⚡ متوسط الوقت: ${avgTime.toFixed(2)}ms`);
    console.log(`   🚀 أسرع وقت: ${minTime}ms`);
    console.log(`   🐌 أبطأ وقت: ${maxTime}ms`);

    // النتيجة النهائية
    console.log('\n' + '='.repeat(80));
    const overallSuccess = (successCount / totalTests) >= 0.8 && (blockedCount / maliciousQueries.length) >= 0.8;
    
    if (overallSuccess) {
      console.log('🎉 النظام يعمل بشكل ممتاز! جاهز للاستخدام.');
    } else {
      console.log('⚠️ النظام يحتاج إلى تحسينات قبل الاستخدام.');
    }
    
    console.log('='.repeat(80));

    // تشغيل الاختبار المحسن
    console.log('\n🔄 تشغيل الاختبار المحسن مع Schema وAgent Profile...\n');
    await runEnhancedSystemTests();

  } catch (error) {
    console.error('❌ خطأ في تشغيل الاختبار:', error);
    process.exit(1);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  main().catch(error => {
    console.error('💥 خطأ غير متوقع:', error);
    process.exit(1);
  });
}

module.exports = { main };

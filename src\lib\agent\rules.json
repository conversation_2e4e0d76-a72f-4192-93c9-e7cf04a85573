{"security_rules": {"sql_injection_patterns": ["'; DROP TABLE", "'; DELETE FROM", "'; UPDATE ", "'; INSERT INTO", "UNION SELECT", "UNION ALL SELECT", "-- ", "/*", "*/", "xp_cmdshell", "sp_executesql", "sp_adduser", "sp_addsrvrolemember", "EXEC(", "EXECUTE(", "OPENROWSET", "OPENDATASOURCE"], "forbidden_operations": ["DROP", "DELETE", "UPDATE", "INSERT", "ALTER", "CREATE", "TRUNCATE", "GRANT", "REVOKE", "DENY", "BACKUP", "RESTORE", "SHUTDOWN", "KILL"], "allowed_operations": ["SELECT", "COUNT", "SUM", "AVG", "MAX", "MIN", "GROUP BY", "ORDER BY", "LIMIT", "WHERE", "HAVING", "JOIN", "INNER JOIN", "LEFT JOIN", "RIGHT JOIN", "DISTINCT", "TOP", "CASE", "WHEN", "THEN", "ELSE", "END"], "max_query_length": 2000, "max_result_rows": 10000, "query_timeout_seconds": 30}, "access_control": {"user_roles": {"admin": {"permissions": ["read", "write", "delete", "admin"], "accessible_tables": ["*"], "accessible_columns": ["*"], "max_rows_per_query": 50000, "can_access_sensitive_data": true}, "manager": {"permissions": ["read", "write"], "accessible_tables": ["tbltemp_ItemsMain", "tbltemp_Inv_MainInvoice"], "accessible_columns": ["*"], "max_rows_per_query": 10000, "can_access_sensitive_data": true, "branch_restrictions": "user_branch_only"}, "employee": {"permissions": ["read"], "accessible_tables": ["tbltemp_ItemsMain"], "accessible_columns": ["ItemName", "Quantity", "Amount", "TheDate", "BranchName"], "max_rows_per_query": 1000, "can_access_sensitive_data": false, "branch_restrictions": "user_branch_only"}, "viewer": {"permissions": ["read"], "accessible_tables": ["tbltemp_ItemsMain"], "accessible_columns": ["ItemName", "Quantity", "TheDate", "BranchName"], "max_rows_per_query": 500, "can_access_sensitive_data": false, "branch_restrictions": "user_branch_only"}}, "sensitive_columns": ["ClientID", "DistributorID", "UserName", "Cost", "Profit", "Discount", "ExchangePrice"], "branch_access_control": true, "time_based_access": {"business_hours_only": false, "allowed_hours": {"start": "06:00", "end": "23:59"}, "weekend_access": true}}, "data_privacy": {"anonymization_rules": {"client_names": {"enabled": true, "method": "mask", "pattern": "عميل-***"}, "user_names": {"enabled": true, "method": "mask", "pattern": "مستخدم-***"}, "phone_numbers": {"enabled": true, "method": "mask", "pattern": "***-***-****"}}, "data_retention": {"max_history_days": 1095, "archive_old_data": true, "delete_after_archive": false}}, "query_validation": {"required_fields": ["query", "explanation", "confidence"], "confidence_threshold": 0.7, "max_joins": 5, "max_subqueries": 3, "require_where_clause": false, "max_group_by_columns": 10, "max_order_by_columns": 5, "allowed_functions": ["COUNT", "SUM", "AVG", "MAX", "MIN", "ROUND", "CEIL", "FLOOR", "ABS", "UPPER", "LOWER", "TRIM", "SUBSTRING", "CONCAT", "DATE", "YEAR", "MONTH", "DAY", "DATEADD", "DATEDIFF", "GETDATE", "NOW"]}, "business_rules": {"financial_constraints": {"max_amount_display": 1000000, "currency_format": "SAR", "decimal_places": 2, "hide_negative_profits": false}, "reporting_constraints": {"max_date_range_days": 365, "require_date_filter": true, "default_date_range": "current_month", "max_products_in_report": 1000, "max_customers_in_report": 500}, "inventory_rules": {"show_zero_stock": true, "show_negative_stock": true, "stock_alert_threshold": 10, "include_inactive_items": false}}, "performance_rules": {"query_optimization": {"force_index_usage": true, "max_execution_time_ms": 30000, "max_memory_usage_mb": 512, "enable_query_cache": true, "cache_duration_minutes": 15}, "result_limits": {"default_limit": 100, "max_limit": 10000, "pagination_size": 50, "enable_streaming": false}}, "audit_rules": {"log_all_queries": true, "log_query_results": false, "log_user_actions": true, "retention_days": 90, "alert_on_suspicious_activity": true, "suspicious_patterns": ["multiple_failed_attempts", "unusual_query_patterns", "large_data_exports", "after_hours_access"]}, "error_handling": {"show_detailed_errors": false, "log_errors": true, "fallback_queries": {"enabled": true, "simple_alternatives": true}, "user_friendly_messages": {"no_data_found": "لم يتم العثور على بيانات للفترة المحددة", "access_denied": "ليس لديك صلاحية للوصول إلى هذه البيانات", "query_too_complex": "الاستعلام معقد جداً، يرجى تبسيطه", "timeout_error": "انتهت مهلة الاستعلام، يرجى تقليل نطاق البيانات"}}, "compliance": {"gdpr_compliance": {"enabled": false, "data_subject_rights": true, "consent_tracking": false}, "local_regulations": {"saudi_data_protection": true, "financial_reporting_standards": true, "tax_compliance": true}}}